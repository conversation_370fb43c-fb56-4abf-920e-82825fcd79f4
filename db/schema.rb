# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_19_121000) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "generation_tasks", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "user_id", null: false
    t.string "task_id"
    t.integer "api_provider", default: 0, null: false
    t.text "prompt"
    t.string "title"
    t.text "style"
    t.text "style_negative"
    t.boolean "custom_mode", default: false, null: false
    t.boolean "instrumental", default: false, null: false
    t.string "ai_model_name", null: false
    t.string "tags"
    t.integer "status", default: 0, null: false
    t.jsonb "response_data", default: {}
    t.jsonb "error_data", default: {}
    t.datetime "completed_at"
    t.integer "duration"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["status"], name: "index_generation_tasks_on_status"
    t.index ["task_id", "api_provider"], name: "index_generation_tasks_on_task_id_and_api_provider", unique: true
    t.index ["user_id"], name: "index_generation_tasks_on_user_id"
  end

  create_table "genres", force: :cascade do |t|
    t.string "name"
    t.string "category"
    t.string "locale"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "identities", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "provider_id", null: false
    t.uuid "user_id", null: false
    t.jsonb "identity_data", null: false
    t.string "provider", null: false
    t.datetime "last_sign_in_at"
    t.string "email", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_identities_on_email"
    t.index ["provider_id", "provider"], name: "index_identities_on_provider_id_and_provider", unique: true
    t.index ["user_id"], name: "index_identities_on_user_id"
  end

  create_table "orders", force: :cascade do |t|
    t.uuid "user_id", null: false
    t.bigint "plan_id"
    t.string "stripe_session_id"
    t.string "stripe_customer_id"
    t.integer "status", default: 0
    t.bigint "amount"
    t.string "currency"
    t.datetime "paid_time"
    t.datetime "date"
    t.jsonb "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["plan_id"], name: "index_orders_on_plan_id"
    t.index ["status"], name: "index_orders_on_status"
    t.index ["stripe_session_id"], name: "index_orders_on_stripe_session_id", unique: true
    t.index ["user_id"], name: "index_orders_on_user_id"
  end

  create_table "page_fields", force: :cascade do |t|
    t.bigint "page_id", null: false
    t.string "key", null: false
    t.text "value"
    t.string "locale", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "page_fields_on_discarded_at"
    t.index ["page_id", "key", "locale"], name: "page_fields_on_page_id_and_key_and_locale", unique: true
    t.index ["page_id", "key"], name: "page_fields_on_page_id_and_key"
    t.index ["page_id", "locale"], name: "page_fields_on_page_id_and_locale"
    t.index ["page_id"], name: "page_fields_on_page_id"
  end

  create_table "pages", force: :cascade do |t|
    t.string "slug", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "pages_on_discarded_at"
    t.index ["slug"], name: "pages_on_slug", unique: true
  end

  create_table "plans", force: :cascade do |t|
    t.string "name", null: false
    t.integer "price", null: false
    t.boolean "is_active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "plan_type", null: false
    t.text "description"
    t.integer "plan_limit", null: false
    t.integer "duration", null: false
    t.string "billing_interval"
    t.string "stripe_product_id"
    t.string "stripe_price_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.uuid "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "songs", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "user_id", null: false
    t.uuid "generation_task_id"
    t.string "title", null: false
    t.text "lyrics"
    t.string "style"
    t.text "prompt"
    t.string "status"
    t.string "audio_url"
    t.string "image_url"
    t.float "duration"
    t.boolean "is_private", default: false, null: false
    t.datetime "discarded_at"
    t.boolean "is_liked", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_songs_on_discarded_at"
    t.index ["generation_task_id"], name: "index_songs_on_generation_task_id"
    t.index ["status"], name: "index_songs_on_status"
    t.index ["user_id"], name: "index_songs_on_user_id"
  end

  create_table "users", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "email"
    t.string "username"
    t.string "avatar_url"
    t.string "stripe_customer_id"
    t.integer "plan_limit", default: 20
    t.integer "plan_used", default: 0
    t.integer "today_used", default: 0
    t.integer "total_used", default: 0
    t.bigint "plan_id"
    t.integer "purchased_credits", default: 0
    t.integer "purchased_credits_used", default: 0
    t.datetime "plan_expire_at"
    t.decimal "paid_amount", precision: 10, scale: 2, default: "0.0"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["plan_id"], name: "index_users_on_plan_id"
    t.index ["stripe_customer_id"], name: "index_users_on_stripe_customer_id", unique: true
    t.index ["username"], name: "index_users_on_username", unique: true
  end

  add_foreign_key "generation_tasks", "users"
  add_foreign_key "identities", "users", on_delete: :cascade
  add_foreign_key "orders", "plans"
  add_foreign_key "orders", "users"
  add_foreign_key "page_fields", "pages"
  add_foreign_key "sessions", "users"
  add_foreign_key "songs", "generation_tasks"
  add_foreign_key "songs", "users"
  add_foreign_key "users", "plans"
end
