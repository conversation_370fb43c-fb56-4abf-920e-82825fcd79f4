class CreateGenerationTasks < ActiveRecord::Migration[8.0]
  def change
    create_table :generation_tasks, id: :uuid, default: -> { "gen_random_uuid()" } do |t|
      t.references :user, type: :uuid, null: false, foreign_key: true
      t.string :task_id # External task ID
      t.integer :api_provider, default: 0, null: false
      t.text :prompt
      t.string :title
      t.text :style
      t.text :style_negative
      t.boolean :custom_mode, default: false, null: false
      t.boolean :instrumental, default: false, null: false
      t.string :ai_model_name, null: false
      t.string :tags
      t.integer :status, default: 0, null: false
      t.jsonb :response_data, default: {}
      t.jsonb :error_data, default: {}
      t.datetime :completed_at
      t.integer :duration # e.g., in seconds

      t.timestamps
    end
    add_index :generation_tasks, [ :task_id, :api_provider ], unique: true
    add_index :generation_tasks, :status
  end
end
