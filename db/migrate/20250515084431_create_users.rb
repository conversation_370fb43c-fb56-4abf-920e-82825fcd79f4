class CreateUsers < ActiveRecord::Migration[8.0]
  def change
    create_table :users, id: :uuid, default: 'gen_random_uuid()' do |t|
      t.string :email
      t.string :username

      t.string :avatar_url
      t.string :stripe_customer_id

      t.integer :plan_limit, default: 20
      t.integer :plan_used, default: 0
      t.integer :today_used, default: 0
      t.integer :total_used, default: 0
      t.references :plan, null: true, foreign_key: true

      t.integer :purchased_credits, default: 0
      t.integer :purchased_credits_used, default: 0

      t.datetime :plan_expire_at
      t.decimal :paid_amount, precision: 10, scale: 2, default: 0.0

      t.datetime :created_at, null: false
      t.datetime :updated_at, null: false
    end
    add_index :users, :email, unique: true
    add_index :users, :username, unique: true
    add_index :users, :stripe_customer_id, unique: true
  end
end
