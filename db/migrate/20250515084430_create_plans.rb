class CreatePlans < ActiveRecord::Migration[8.0]
  def change
    create_table :plans do |t|
      t.string :name, null: false
      t.integer :price, null: false
      t.boolean :is_active, default: true
      t.datetime :created_at, null: false
      t.datetime :updated_at, null: false
      t.integer :plan_type, null: false
      t.text :description
      t.integer :plan_limit, null: false
      t.integer :duration, null: false
      t.string :billing_interval
      t.string :stripe_product_id
      t.string :stripe_price_id
    end
  end
end
