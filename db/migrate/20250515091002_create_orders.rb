class CreateOrders < ActiveRecord::Migration[8.0]
  def change
    create_table :orders do |t| # Matching your schema's default ID for orders
      t.references :user, type: :uuid, null: false, foreign_key: true # Creates index on user_id
      t.references :plan, foreign_key: true # Creates index on plan_id; Plan ID is bigint in your schema
      t.string :stripe_session_id
      t.string :stripe_customer_id # Often on the User model
      t.integer :status, default: 0
      t.bigint :amount # Matches your schema
      t.string :currency
      t.datetime :paid_time
      t.datetime :date
      t.jsonb :metadata, default: {}

      t.timestamps
    end
    add_index :orders, :stripe_session_id, unique: true
    add_index :orders, :status
  end
end
