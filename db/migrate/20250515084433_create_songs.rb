class CreateSongs < ActiveRecord::Migration[8.0]
  def change
    create_table :songs, id: :uuid, default: -> { "gen_random_uuid()" } do |t|
      t.references :user, type: :uuid, null: false, foreign_key: true
      t.references :generation_task, type: :uuid, null: true, foreign_key: true

      t.string :title, null: false
      t.text :lyrics
      t.string :style
      t.text :prompt
      t.string :status
      t.string :audio_url
      t.string :image_url
      t.float :duration
      t.boolean :is_private, default: false, null: false

      t.datetime :discarded_at
      t.boolean :is_liked, default: false, null: false
      t.timestamps
    end
    add_index :songs, :status
    add_index :songs, :discarded_at
  end
end
