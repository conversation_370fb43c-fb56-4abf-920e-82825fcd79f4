class CreatePageFields < ActiveRecord::Migration[8.0]
  def change
    create_table :page_fields do |t|
      t.bigint   :page_id, null: false
      t.string   :key, null: false
      t.text     :value
      t.string   :locale, null: false
      t.datetime :created_at, null: false
      t.datetime :updated_at, null: false
      t.datetime :discarded_at
    end

    add_index :page_fields, :discarded_at, name: "page_fields_on_discarded_at"
    add_index :page_fields, :page_id, name: "page_fields_on_page_id"
    add_index :page_fields, %i[page_id key], name: "page_fields_on_page_id_and_key"
    add_index :page_fields, %i[page_id locale], name: "page_fields_on_page_id_and_locale"
    add_index :page_fields, %i[page_id key locale], unique: true, name: "page_fields_on_page_id_and_key_and_locale"

    add_foreign_key :page_fields, :pages, column: :page_id
  end
end
