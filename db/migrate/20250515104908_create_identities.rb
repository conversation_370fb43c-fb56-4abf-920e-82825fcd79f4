class CreateIdentities < ActiveRecord::Migration[8.0]
  def change
    create_table :identities, id: :uuid do |t|
      t.string :provider_id, null: false
      t.references :user, type: :uuid, null: false, foreign_key: { on_delete: :cascade }
      t.jsonb :identity_data, null: false

      t.string :provider, null: false
      t.datetime :last_sign_in_at
      t.string :email, null: false

      t.timestamps
    end

    add_index :identities, [ :provider_id, :provider ], unique: true
    add_index :identities, :email
  end
end
