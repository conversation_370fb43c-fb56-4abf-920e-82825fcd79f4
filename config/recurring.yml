# Example configuration (commented out)
# production:
#   periodic_cleanup:
#     class: CleanSoftDeletedRecordsJob
#     queue: background
#     args: [ 1000, { batch_size: 500 } ]
#     schedule: every hour
#   periodic_command:
#     command: "SoftDeletedRecord.due.delete_all"
#     priority: 2
#     schedule: at 5am every day

# User plan management tasks
production:
  expire_plans:
    class: ExpirePlansJob
    queue: default
    schedule: every hour
    description: "Check for expired user plans and reset to free"

  reset_daily_limits:
    class: ResetDailyLimitsJob
    queue: default
    schedule: at 12am every day
    description: "Reset daily usage tracking for all users (today_used=0), and reset free users' plan limits"

development:
  expire_plans:
    class: ExpirePlansJob
    queue: default
    schedule: at 12am every day
    description: "Check for expired user plans and reset to free"

  reset_daily_limits:
    class: ResetDailyLimitsJob
    queue: default
    schedule: at 12am every day
    description: "Reset daily usage tracking for all users (today_used=0), and reset free users' plan limits"
