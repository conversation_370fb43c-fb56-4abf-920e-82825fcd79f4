# See https://github.com/basecamp/solid_queue#configuration for options
# This is a basic configuration that should work for most applications
production:
  dispatchers:
    - polling_interval: 1
      batch_size: 500
  workers:
    - queues: \"*\"
      threads: 5
      processes: 1
      polling_interval: 0.1

development:
  dispatchers:
    - polling_interval: 1
      batch_size: 100
  workers:
    - queues: \"*\"
      threads: 2
      polling_interval: 0.5
# You can add more environments here, like test, staging, etc.
# Ensure that your database adapter in config/database.yml is set to a persistent one, like postgresql or mysql.
# SQLite in-memory won't work for Solid Queue.
