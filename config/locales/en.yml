en:
  generation_tasks:
    generator_interface:
      genre: "Genre"
      genre_placeholder: "Type to search or add..."
      moods: "Moods"
      moods_placeholder: "Type to search or add..."
      instrument: "Instrument"
      instrument_placeholder: "Type to search or add..."
      description_mode: "Description Mode"
      lyrics_mode: "Lyrics Mode"
      clear_all: "Clear All"
      description: "Description"
      description_placeholder: "Describe your song, e.g., epic movie, orchestral, uplifting..."
      surprise_me: "Surprise Me"
      vocal: "Vocal"
      male_voice: "Male Voice"
      female_voice: "Female Voice"
      instrumental: "Instrumental"
      show_more_options: "Show more options"
      hide_advanced_options: "Hide advanced options"
      tempo: "Tempo"
      tempo_slow: "60-80 BPM"
      tempo_medium: "80-120 BPM"
      tempo_fast: "120-160 BPM"
      duration: "Duration"
      duration_2_min: "2 min"
      duration_4_min: "4 min"
      lyrics: "Lyrics"
      lyrics_placeholder: "Enter your lyrics here..."
      generate_lyrics: "Generate Lyrics"
      title: "Title"
      title_placeholder: "Add a song title"
      title_error: "You must enter a title"
      custom_style: "Custom Style"
      style: "Style"
      style_placeholder: "Describe the musical style you want, e.g., energetic pop, dark ambient, jazz fusion..."
      duration_30_s: "30s"
      duration_60_s: "60s"
      duration_1_min: "1 min"
      create_music: "Create Music"
      cost: "Cost: 10 credits"
    quick_add_tags:
      genres:
        ["Jazz", "Rock", "Psychedelic", "Electronic", "Symphonic Black Metal"]
      moods: ["Happy", "Sad", "Energetic", "Calm"]
      instruments: ["Acoustic", "Guitar", "Electro", "Synth", "Piano"]
  layouts:
    sidebar:
      open_sidebar: "Open sidebar"
      create_music: "Create Music"
      plans_and_credits: "Plans & Credits"
      upgrade_plan: "Upgrade Plan"
      buy_credits: "Buy Credits"
      logout: "Logout"
      login: "Login"
      language: "Language"
      support: "Support"
      email_copied: "Email copied to clipboard: %{email}"
  songs:
    index:
      all: "All"
      liked: "Liked"
      search_placeholder: "Search your songs..."
    show:
      like: "Like"
      liked: "Liked"
      play: "Play"
      pause: "Pause"
      create_first_song: "Create Your First Song"
      transform_ideas: "Transform your ideas into professional music with just a few clicks"
      ai_powered: "AI-Powered"
      instant_results: "Instant Results"
    ai_generated: "AI Generated"
  pricing:
    index:
      title: "Choose Your Plan"
      monthly_billing: "Billed Monthly"
      yearly_billing: "Billed Yearly"
      save_20_percent: "Save 20%"
    credits:
      title: "Top Up Credits"
      subtitle: "Need more credits? Pick the pack that's right for you."
      packages_title: "Credit Packages"
      payment_confirmation: "You are about to purchase credits. Proceed to payment?"
      purchase_credits: "Purchase Credits"
      upgrade_plan_instead: "Upgrade Plan Instead"
  sessions:
    new:
      login_title: "Login"
      continue_with_email: "Continue with Email"
      login_code: "Login Code"
      verify_otp_and_login: "Verify & Login"
      resend_code: "Resend Code"
      resend_in_html: "Resend in %{count}s"
      resend_failed: "Resend Failed"
      login_with_google: "Login With Google"
      or: "OR"
      your_email: "Your Email"
      invalid_email: "Please enter a valid email address"
    login_for_free_to_listen: "Login for free to listen"
  shared:
    subscription_card:
      title: "Subscribe to Pro for More Exclusive Perks"
      priority_queue: "Priority generation queue"
      running_jobs: "10 concurrent running jobs"
      subscribe: "Subscribe"
    generation_task_card:
      generated_song: "Generated Song"
      generating: "Generating..."
      generated_successfully: "Generated Successfully"
      processing_audio: "Processing audio..."
      creating_content: "Creating content..."
      streaming_ready: "Streaming ready"
      generation_in_progress: "Generation in progress..."
    song_card:
      untitled_song: "Untitled Song"
    plan_card:
      free_plan: "Free Plan"
      todays_usage: "Today's Usage"
      upgrade_plan: "Upgrade Plan"
      plan_usage: "Plan Usage"
      buy_more_credits: "Buy more credits"
      unlimited_plan: "Unlimited Plan"
      manage_plan: "Manage Plan"
      credits: "Credits"
      unlimited: "Unlimited"
      purchased_credits: "Purchased Credits"
      basic_plan: "Basic Plan"
      pro_plan: "Pro Plan"
      credits_plan: "Credits Plan"
    unlimited_credits: "Unlimited Credits"
    credits: "Credits"
    pricing_plan:
      choose_plan: "Choose Plan"
      current_plan: "Current Plan"
      upgrade: "Upgrade"
      tooltip_already_subscribed: "You are already on this plan"
      tooltip_cant_select: "You can't select this plan"
    close: "Close"
    back_to_home: "Back to Home"
  flash:
    generation_tasks:
      create:
        success: "Generation task successfully started!"
        insufficient_credits: "You don't have enough credits to generate music. Please upgrade your plan or buy more credentials."
        concurrency_limit_reached: "You can only have %{limit} generation jobs running at the same time on your current plan. Please wait for them to finish or upgrade your plan."
    orders:
      create:
        alert: "Could not create order: %{errors}"
      success:
        notice: "🎉 Successfully purchased %{credits_text}!"
        alert_processing_failed: "Payment processing failed or is still pending. If your payment has been deducted, please contact support."
        alert_not_found: "Order not found. If payment was processed, please contact support."
      cancel:
        notice: "Payment was cancelled."
      payment_error:
        alert: "Could not process payment. Please try again."
    fail:
      error: "Generation failed. Your credits have been refunded."
  auth:
    errors:
      email_delivery_failed: "We couldn't deliver the OTP email. Please check the email address or try again later."
      too_frequent: "You're trying too frequently. Please wait a moment."
      verification_failed: "The verification code you entered is incorrect or has expired. Please try again."
      login_failed: "Login failed. Please check your credentials or try another method."
      invalid_code: "The verification code you entered is incorrect or has expired. Please try again."
      invalid_email: "Please enter a valid email address"
    sign_out_success: "You have been signed out successfully."
    sign_in_success: "Signed in successfully."
    verification_code_resent: "A new verification code has been sent."
    verification_code_sent: "A verification code has been sent to %{email}."
    check_your_email:
      title: "Check your email"
      description: "We sent you a login link. If you don't receive it in a few minutes, check your spam folder."
      resend: "Didn't receive an email?"
      resend_link: "Resend"
  otp_mailer:
    otp_email:
      subject: "Your AI Music Generator login code is %{otp}"
      hello: "Hello %{email},"
      otp_is: "Your Login Code is: %{otp}"
      otp_is_html: "Yourj login code is: <strong>%{otp}</strong>"
      code_expires: "This code will expire in %{minutes} minutes."
      ignore_if_not_requested: "If you did not request this, please ignore this email."
  plans:
    features:
      credits_per_month: "%{value} Credits per month"
      commercial_terms: "Commercial terms"
      credit_top_ups: "Credit top-ups"
      concurrent_jobs: "%{value} concurrent generation jobs"
      generation_speed:
        standard: "Standard generation speed"
        priority: "Priority generation speed"
      advanced_features: "Advanced features"
      unlimited_credits: "Unlimited credits"
      all_premium_features: "All premium features"
      premium_support: "Premium support"
      yearly_saving: "Save 20% annually"
    names:
      free: "Free"
      basic: "Basic"
      pro: "Pro"
      unlimited: "Unlimited"
    billing_intervals:
      month: "Month"
      year: "Year"
    credit_packages:
      50_credits: "50 Credits"
      100_credits: "100 Credits"
      200_credits: "200 Credits"
      400_credits: "400 Credits"
