require "aws-sdk-s3"

r2_credentials = Rails.application.credentials.dig(:cloudflare, :r2)
account_id = nil
access_key_id = nil
secret_access_key = nil
bucket_name = nil
if r2_credentials
  account_id = r2_credentials[:account_id]
  access_key_id = r2_credentials[:access_key_id]
  secret_access_key = r2_credentials[:secret_access_key]
  bucket_name = r2_credentials[:bucket_name]
  public_url_base = r2_credentials[:public_url_base]
end

unless account_id && access_key_id && secret_access_key && bucket_name
  Rails.logger.warn("Cloudflare R2 configuration is incomplete. Asset uploads to R2 will be disabled.")
  R2_CLIENT = nil
  R2_BUCKET_NAME = nil
  R2_PUBLIC_URL_BASE = nil
else
  endpoint = "https://#{account_id}.r2.cloudflarestorage.com"

  R2_CLIENT = Aws::S3::Client.new(
    endpoint:          endpoint,
    access_key_id:     access_key_id,
    secret_access_key: secret_access_key,
    region:            "auto"
  )

  R2_BUCKET_NAME = bucket_name
  R2_PUBLIC_URL_BASE = public_url_base || "#{endpoint}/#{bucket_name}"

  Rails.logger.info "Cloudflare R2 client configured for bucket: #{R2_BUCKET_NAME}"
end
