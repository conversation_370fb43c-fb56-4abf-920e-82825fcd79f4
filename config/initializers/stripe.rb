require "stripe"

# Fallback to placeholder values for testing
stripe_secret = Rails.application.credentials.dig(:stripe, :secret_key) || "sk_test_placeholder"
stripe_publishable = Rails.application.credentials.dig(:stripe, :publishable_key) || "pk_test_placeholder"
stripe_webhook = Rails.application.credentials.dig(:stripe, :webhook_secret) || "whsec_placeholder"

Stripe.api_key = stripe_secret

Rails.application.configure do
  config.stripe = {
    publishable_key: stripe_publishable,
    secret_key: stripe_secret,
    webhook_secret: stripe_webhook
  }
end
