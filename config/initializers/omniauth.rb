Rails.application.config.middleware.use OmniAuth::Builder do
  google_client_id     = Rails.application.credentials.dig(:oauth, :google_client_id)
  google_client_secret = Rails.application.credentials.dig(:oauth, :google_client_secret)

  provider :google_oauth2,
           google_client_id,
           google_client_secret,
           {
             scope: "email, profile",
             prompt: "select_account",
             image_aspect_ratio: "square",
             image_size: 50,
             # Send users back to our dedicated controller action
             callback_path: "/sessions/google_callback"
           }
end

OmniAuth.config.path_prefix = "/auth"

OmniAuth.config.allowed_request_methods = [ :get, :post ]

OmniAuth.config.silence_get_warning = true

OmniAuth.config.on_failure = Proc.new do |env|
  OmniAuth::FailureEndpoint.new(env).redirect_to_failure
end
