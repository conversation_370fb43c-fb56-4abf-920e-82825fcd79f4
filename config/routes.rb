Rails.application.routes.draw do
  # Redirect /en and /en/* paths to the non-prefixed version, while preserving query parameters
  get "/en(/*path)", to: redirect { |params, req|
    query_string = req.query_string.present? ? "?#{req.query_string}" : ""
    params[:path] ? "/#{params[:path]}#{query_string}" : "/#{query_string}"
  }

  # I18n routes that are dynamically generated from your available locales
  scope "(:locale)", locale: /#{I18n.available_locales.join("|")}/ do
    root "home#index"

    # Generation Tasks
    resources :generation_tasks, only: [ :create ] do
      collection do
        post :generate_surprise_me_stream
        post :generate_lyrics_stream
      end
    end

    resources :songs, only: [ :show, :index ] do
      member do
        patch :toggle_like
      end
    end

    # --- User Authentication (all handled by SessionsController) ---

    resources :sessions, only: [ :new, :destroy ] do
      collection do
        # email otp
        post  "request_otp"
        post  "verify_otp"
        # google oauth
        get   "google"
        get   "google_callback"
      end
    end

    get    "/login",  to: "sessions#new",     as: "login"
    delete "/logout", to: "sessions#destroy", as: "logout"

    # --- End User Authentication ---

    # Webhooks
    namespace :webhooks do
      post :stripe
      post :api_box
    end

    resources :pricing, only: [ :index ] do
      collection do
        get :credits
      end
    end

    resources :orders, only: [ :create ] do
      collection do
        get :success
        get :cancel
      end
    end

    resources :genres, only: [ :index ]

    # Static Pages
    get "terms-of-service", to: "pages#terms_of_service"
    get "privacy-policy", to: "pages#privacy_policy"
  end
end
