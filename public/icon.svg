<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" width="128" height="128">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea"/>
      <stop offset="100%" style="stop-color:#764ba2"/>
    </linearGradient>
    <linearGradient id="noteGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff"/>
      <stop offset="100%" style="stop-color:#f1f5f9"/>
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Rounded square background -->
  <rect x="8" y="8" width="112" height="112" rx="28" ry="28" fill="url(#bg)" filter="url(#shadow)"/>

  <!-- Music note with smoother curves -->
  <!-- Note head (oval shape for better music note appearance) -->
  <ellipse cx="52" cy="88" rx="12" ry="10" fill="url(#noteGradient)" transform="rotate(-15 52 88)"/>

  <!-- Note stem -->
  <rect x="60" y="38" width="7" height="50" fill="url(#noteGradient)" rx="2"/>

  <!-- Flag with smoother curves -->
  <path d="M 67 38 C 75 34, 85 36, 88 42 C 86 46, 80 48, 75 46 C 72 44, 70 42, 67 40 Z" fill="url(#noteGradient)"/>
  <path d="M 67 45 C 73 42, 80 43, 82 47 C 81 50, 77 51, 74 50 C 71 49, 69 47, 67 46 Z" fill="url(#noteGradient)"/>

  <!-- Additional musical elements for richness -->
  <!-- Small sparkle effects -->
  <circle cx="35" cy="45" r="1.5" fill="white" opacity="0.8"/>
  <circle cx="95" cy="75" r="1" fill="white" opacity="0.6"/>
  <circle cx="42" cy="65" r="1" fill="white" opacity="0.7"/>

  <!-- Subtle sound waves -->
  <path d="M 28 55 Q 32 52, 28 49" stroke="white" stroke-width="1.5" fill="none" opacity="0.4" stroke-linecap="round"/>
  <path d="M 24 60 Q 30 56, 24 52" stroke="white" stroke-width="1.5" fill="none" opacity="0.3" stroke-linecap="round"/>
</svg>
