Why can’t I hear my lyrics?
<PERSON><PERSON> has a strong random element to keep the songs interesting. Sometimes a clip won’t sing, even with the same prompt.

But if both clips fail to sing, there might be a problem with the style prompt or metatags.

Some common problems:
<PERSON><PERSON> doesn’t know how to sing this text.
<PERSON>rp decided the song needs a very long introduction, maybe longer than the clip.
<PERSON><PERSON> has received conflicting instructions and choked.
Describe the voice
It can help to add a specific voice description to the Style Prompt:

upbeat female vocals
sultry male singer
<PERSON><PERSON> more about Voice Tags

Musical Intro is too long
Prompts and style descriptions seem to work best when they are short and simple. Try fewer or simpler tags before the first vocal.

❌ Bad:
[Intro Drums]
[Intro Bass]
[Intro Guitar]

✅︎ better:
[Instrumental Build]

Learn more about structuring a song with Metatags

Get the Voice First
Try adding a short vocalization before the intro prompt, or even an a cappella. Get the voice to do something (anything) before the music starts.

(Ahh ahh ahh)

[Catchy Hook]

[Verse 1]
Dancing in the neon lights…