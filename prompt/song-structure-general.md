Song Structure Tags

There are other parts to a song than just the verse/chorus pattern.

We can influence the song structure with metatags, although the ai tends to have a mind of its own and follow it’s own pattern.

[Intro]
This one is notoriously unreliable. It’s probably better to describe it like an instrumental break.

[Short Instrumental Intro]
[Hook]
A hook is a repetitive phrase or instrumental. Try repeating a short line 2 – 4 times with or without the label.

[Catchy Hook]
[Break]
A break is a few bars of the song where the lead instruments or singer go silent, and the accompanying instruments play. A [Break] can sometimes be used strategically to interrupt the current pattern.

[Break]
[Percussion Break]
[Interlude]
Interlude is a useful tag to create an instrumental section within the lyrics.

[melodic interlude]
[Outro]
An Outro can help to prime the song to end, and may create a loop to fade out in post edit.

Refrain seems to get more ‘creative’ when wrapping up the end of the song, while Big Finish may change the melody or tempo to create a climax.

[Outro]
[Refrain]
[Big Finish]
[End]
An end tag in the lyrics may work best alone as its own clip. Clear the Style Prompt, or add ‘End’ to the style description.

[End]
[Fade Out]
[Fade to End]
See: How do I end the song?

As always, prompting an AI is not like paying someone to edit your music on Fiverr. The reliability of these tags can be influenced by the lyrics, the song cycle, and the AI just being random.