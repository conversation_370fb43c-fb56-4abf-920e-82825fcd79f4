Should my Style Prompt use commas?

According to <PERSON><PERSON> devs, commas used in the Style Prompt will create a ‘balanced’ list where each item has an equal chance of contributing to the song.

Style Prompts without commas should follow language conventions. Typically one word modifies another, and style words may work better changed to description text.

The prompt:
Rock, Guitars
is different than:
Rock Guitars

Combine commas and descriptions
A typical Style Prompt might combine short descriptions in a comma-separated list

dreamy shoegaze, pulsing Yamaha bass, raw distorted guitars, warm synthpads, slow female whisper

For the list to be weighted equally, the prompt needs to include a space after each comma.

Other punctuation should work similarly to commas.