class SongsController < ApplicationController
  before_action :require_authentication
  before_action :set_song, only: [ :show, :toggle_like ]

  # GET /songs
  # Handles initial load of the song library and search queries.
  def index
    @query_from_user = params[:query].to_s.strip
    song_filter = current_user.songs

    song_filter = song_filter.search_by_title(@query_from_user) if @query_from_user.present?
    song_filter = song_filter.liked if params[:filter] == "liked"

    @songs = song_filter.order(created_at: :desc)

    # Every render knows about tasks that haven't finished yet
    @tasks_in_progress = current_user
                           .generation_tasks
                           .in_progress
                           .order(created_at: :desc)

    respond_to do |format|
      format.html
    end
  end

  # GET /songs/:id
  def show
    authorize_song!
    if request.headers["Turbo-Frame"] == "song_detail_area"
      render :show, layout: false
    else
      head :not_found
    end
  end
  # PATCH /songs/:id/toggle_like
  # Toggles the is_liked boolean on a song.
  # Model callback handles broadcasting the change to all subscribers.
  def toggle_like
    authorize_song!
    @song.with_lock do
      @song.update!(is_liked: !@song.is_liked)
    end
    head :no_content           # Turbo waits for the ActionCable update
  end

  private

  def set_song
    @song = Song.find(params[:id])
  end

  def authorize_song!
    head :forbidden unless @song.user_id == current_user.id
  end
end
