class GenresController < ApplicationController
  allow_unauthenticated_access only: [ :index ]

  def index
    @query_from_user = params[:query].to_s.strip # Ensure it's a string and strip whitespace
    @target_id       = params[:target_id]
    @category        = params[:category]

    @suggestions = Genre.search_by_name(@query_from_user, @category, I18n.locale.to_s)

    respond_to do |format|
      format.html
    end
  end
end
