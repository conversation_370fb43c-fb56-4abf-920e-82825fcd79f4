class HomeController < ApplicationController
  allow_unauthenticated_access only: :index

  def index
    if current_user
      load_user_dashboard
    else
      load_guest_homepage
    end
  end

  private

  def load_user_dashboard
    @songs = current_user.songs.order(created_at: :desc)
    @first_song = @songs.first
    @tasks_in_progress = current_user.generation_tasks.in_progress.order(created_at: :desc)
  end

  def load_guest_homepage
    @songs = []
    @first_song = nil
    @tasks_in_progress = []
    @page = Page.active.find_by(slug: "/")
  end
end
