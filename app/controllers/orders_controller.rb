class OrdersController < ApplicationController
  def create
    @plan = Plan.find(params[:plan_id])
    @order = current_user.orders.build(order_params)

    if @order.save
      redirect_to_stripe_checkout
    else
      redirect_to credits_pricing_index_path, alert: t("flash.orders.create.alert", errors: @order.errors.full_messages.join(", "))
    end
  end

  def success
    session_id = params[:session_id]
    @order = current_user.orders.find_by(stripe_session_id: session_id)

    if @order
      if @order.status.in?([ "paid", "completed" ])
        credits_text = @order.plan.plan_limit == -1 ? t("shared.unlimited_credits") : "#{@order.plan.plan_limit} #{t('shared.credits')}"
        redirect_to root_path, notice: t("flash.orders.success.notice", credits_text: credits_text)
      else
        redirect_to credits_pricing_index_path, alert: t("flash.orders.success.alert_processing_failed")
      end
    else
      redirect_to credits_pricing_index_path, alert: t("flash.orders.success.alert_not_found")
    end
  end

  def cancel
    redirect_to credits_pricing_index_path, notice: t("flash.orders.cancel.notice")
  end

  private

  def order_params
    {
      plan: @plan,
      amount: @plan.price,
      currency: "USD",
      date: Time.current
    }
  end

  def redirect_to_stripe_checkout
    session = @order.create_checkout_session!(success_url, cancel_url, I18n.locale.to_s)
    redirect_to session.url, allow_other_host: true
  rescue => e
    Rails.logger.error "Order creation error: #{e.message}"
    redirect_to credits_pricing_index_path, alert: t("flash.orders.payment_error.alert")
  end

  def success_url
    "#{request.base_url}/#{I18n.locale}/orders/success?session_id={CHECKOUT_SESSION_ID}"
  end

  def cancel_url
    url_for(controller: "orders", action: "cancel", locale: I18n.locale, only_path: false)
  end
end
