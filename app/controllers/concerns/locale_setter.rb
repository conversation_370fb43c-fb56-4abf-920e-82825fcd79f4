module LocaleSetter
  extend ActiveSupport::Concern

  included do
    before_action :set_locale
  end

  private

  def set_locale
    I18n.locale = extract_locale || I18n.default_locale
  end

  def extract_locale
    locale = params[:locale]
    locale.to_sym if locale.present? && I18n.available_locales.include?(locale.to_sym)
  end

  # Used in views to generate URLs with the correct locale prefix
  def default_url_options
    # Only add the locale parameter for non-default locales
    { locale: I18n.locale == I18n.default_locale ? nil : I18n.locale }
  end
end
