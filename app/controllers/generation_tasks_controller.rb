# app/controllers/generation_tasks_controller.rb
class GenerationTasksController < ApplicationController
  include ActionController::Live
  allow_unauthenticated_access only: [ :generate_surprise_me_stream, :generate_lyrics_stream ]

  def create
    @generation_task = GenerationTask.create_from_form(current_user, generation_task_params)
    current_user.reload

    flash.now[:success] = t("flash.generation_tasks.create.success")

    respond_to do |format|
      format.turbo_stream { render turbo_stream: turbo_stream.replace("toast-container", partial: "shared/flash_messages") }
      format.html { redirect_to root_path, notice: t(".notice") }
    end
  rescue ActiveRecord::RecordInvalid => e
    if insufficient_credits?(e.record)
      show_insufficient_credits_flash
    elsif concurrency_limit_reached?(e.record)
      flash.now[:alert] = t("flash.generation_tasks.create.concurrency_limit_reached", limit: current_user.plan_concurrency_limit)
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("toast-container", partial: "shared/flash_messages") }
        format.html { redirect_to root_path }
      end
    else
      # Handle other validation errors
      flash.now[:alert] = e.record.errors.full_messages.join(", ")
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("toast-container", partial: "shared/flash_messages") }
        format.html { redirect_to root_path, alert: flash.now[:alert] }
      end
    end
  end

  # Stream surprise me content generation using Rails broadcasting
  def generate_surprise_me_stream
    target_id = params[:target_id] || "openai_stream_target"
    session_id = session.id&.to_s || request.remote_ip

    # Start async streaming via job queue
    GenerateStreamJob.perform_later("surprise_me", params[:locale], target_id, session_id)

    # Respond immediately to close the form submission
    respond_to do |format|
      format.turbo_stream { head :ok }
    end
  end

  # Stream lyrics generation using Rails broadcasting
  def generate_lyrics_stream
    target_id = params[:target_id] || "openai_stream_target"
    session_id = session.id&.to_s || request.remote_ip

    # Start async streaming via job queue
    GenerateStreamJob.perform_later("lyrics", params[:locale], target_id, session_id)

    # Respond immediately to close the form submission
    respond_to do |format|
      format.turbo_stream { head :ok }
    end
  end

  private

  def generation_task_params
    params.require(:generation_task).permit(
      :description, :lyrics, :style, :title, :custom_style_enabled, :instrumental, :use_custom_style,
      :tempo, :duration, :vocals,
      genres: [], moods: [], instruments: []
    )
  end

  def concurrency_limit_reached?(record)
    return false unless record.user.present?

    limit = record.user.plan_concurrency_limit
    in_progress_count = record.user.generation_tasks.in_progress.count
    in_progress_count >= limit
  end

  def insufficient_credits?(record)
    # Check if user actually has insufficient credits, regardless of error message language
    record.user.present? && !record.user.has_sufficient_credits?
  end

  def show_insufficient_credits_flash
    flash.now[:alert] = t("flash.generation_tasks.create.insufficient_credits")

    respond_to do |format|
      format.turbo_stream { render turbo_stream: turbo_stream.replace("toast-container", partial: "shared/flash_messages") }
      format.html { redirect_to credits_pricing_index_path, alert: flash[:alert] }
    end
  end

  def set_stream_headers
    response.headers["Content-Type"] = "text/plain"
    response.headers["Cache-Control"] = "no-cache"
    response.headers["Connection"] = "keep-alive"
  end
end
