class SessionsController < ApplicationController
  # Allow public access to login, OTP flows, and Google OAuth endpoints

  allow_unauthenticated_access except: [ :destroy ]

  before_action :set_turbo_frame_variant, only: :new

  def new
    @form = OtpVirtualForm.new
    @page = Page.active.find_by(slug: "/")

    # View selection is handled automatically by variants.
  end

  rate_limit to: 10, within: 1.minute, only: [ :request_otp ],
            with: -> {
              respond_to do |format|
                format.html { redirect_to login_path, alert: t("auth.errors.too_frequent") }
                format.turbo_stream {
                  flash.now[:error] = t("auth.errors.too_frequent")
                  render turbo_stream: turbo_stream.update("flash_messages",
                    partial: "shared/flash_messages"),
                    status: :too_many_requests
                }
              end
            }

  rate_limit to: 10, within: 3.minutes, only: [ :verify_otp ],
            with: -> {
              respond_to do |format|
                format.html { redirect_to login_path, alert: t("auth.errors.too_frequent") }
                format.turbo_stream {
                  flash.now[:error] = t("auth.errors.too_frequent")
                  render turbo_stream: turbo_stream.update("flash_messages",
                    partial: "shared/flash_messages"),
                    status: :too_many_requests
                }
              end
            }

  def destroy
    terminate_session
    flash[:notice] = t("auth.sign_out_success")
    redirect_to root_path
  end

  # 处理 Email 输入，发送 OTP
  def request_otp
    @email = params.dig(:otp_virtual_form, :email)
    @form = OtpVirtualForm.new(email: @email, form_type: :request)

    if @form.save
      session[:otp_token] = @form.token
      session[:otp_email] = @email
      session[:otp_sent_at] = Time.current.to_i

      respond_to do |format|
        format.turbo_stream
        format.html {
          flash[:notice] = params.dig(:otp_virtual_form, :resend) ?
            t("auth.verification_code_resent") :
            t("auth.verification_code_sent", email: @email)
          redirect_to login_path
        }
        format.json do
          flash.now[:notice] = t("auth.verification_code_resent")
          message_html = render_to_string(partial: "shared/simple_flash_alert", formats: [ :html ])
          render json: { status: "ok", message_html: message_html }, status: :ok
        end
      end
    else
      # 使用 Turbo Stream 动态更新错误信息
      error_message = @form.errors.full_messages.first || t("auth.errors.verification_failed")
      flash.now[:error] = error_message
      render turbo_stream: turbo_stream.update("auth_flash_messages", partial: "shared/simple_flash_alert"), status: :unprocessable_entity
    end
  end

  # 处理 OTP 验证
  def verify_otp
    token = session[:otp_token]
    otp = params.dig(:otp_virtual_form, :otp)
    email = session[:otp_email]

    # 使用虚拟表单模型处理 OTP 验证
    @form = OtpVirtualForm.new(email: email, otp: otp, token: token, form_type: :verify)

    if @form.save
      user = User.find_or_create_by_email(email)
      session.delete(:otp_token)
      session.delete(:otp_email)
      session.delete(:otp_sent_at)

      start_new_session_for user

      flash[:notice] = t("auth.sign_in_success")

      # After a successful state-changing POST request, we redirect.
      # Turbo will follow this redirect, resulting in a full page load
      # that reflects the new logged-in state.
      redirect_to after_authentication_url
    else
      # On failure, respond with Turbo Stream to show errors without a full page reload.
      error_message = @form.errors.full_messages.first || t("auth.errors.verification_failed")
      flash.now[:error] = error_message
      render turbo_stream: turbo_stream.update("auth_flash_messages", partial: "shared/simple_flash_alert"), status: :unprocessable_entity
    end
  end

  def google
    session[:return_to] = params[:return_to] if params[:return_to].present?
    redirect_to "/auth/google_oauth2", allow_other_host: true
  end

  def google_callback
    auth_hash = request.env["omniauth.auth"]

    if auth_hash.blank?
      redirect_to login_path(locale: I18n.locale), alert: t("auth.errors.login_failed")
      return
    end

    begin
      user = User.find_or_create_from_omniauth(auth_hash)
      start_new_session_for user
      redirect_to after_authentication_url, notice: t("auth.sign_in_success")
    rescue StandardError => e
      Rails.logger.error("OmniAuth Error: #{e.message}\nBacktrace: #{e.backtrace.join("\n")}")
      redirect_to login_path(locale: I18n.locale), alert: t("auth.errors.login_failed")
    end
  end

  # Handles OAuth failure
  def failure
    redirect_to login_path(locale: I18n.locale), alert: t("auth.errors.login_failed")
  end

  def set_turbo_frame_variant
    request.variant = :turbo_frame if turbo_frame_request?
  end
end
