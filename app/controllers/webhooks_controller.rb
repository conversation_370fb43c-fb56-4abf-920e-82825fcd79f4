class WebhooksController < ActionController::Base
  skip_forgery_protection

  # === STRIPE PAYMENT WEBHOOKS ===
  # Primary payment processing endpoint - processes ALL payments
  def stripe
    payload = request.body.read
    sig_header = request.env["HTTP_STRIPE_SIGNATURE"]
    endpoint_secret = Rails.application.config.stripe[:webhook_secret]

    begin
      event = Stripe::Webhook.construct_event(payload, sig_header, endpoint_secret)
    rescue JSON::ParserError
      Rails.logger.error "[WebhooksController#stripe] Invalid JSON payload"
      return head :bad_request
    rescue Stripe::SignatureVerificationError
      Rails.logger.error "[WebhooksController#stripe] Invalid signature"
      return head :bad_request
    end

    case event["type"]
    when "checkout.session.completed"
      handle_checkout_session_completed(event["data"]["object"])
    when "payment_intent.succeeded"
      handle_payment_intent_succeeded(event["data"]["object"])
    when "payment_intent.payment_failed"
      handle_payment_intent_failed(event["data"]["object"])
    else
      Rails.logger.info "[WebhooksController#stripe] Unhandled event type: #{event['type']}"
    end

    head :ok
  rescue => e
    Rails.logger.error "[WebhooksController#stripe] Error processing webhook: #{e.message}"
    head :internal_server_error
  end

  # POST /webhooks/api_box
  def api_box
    payload = request.raw_post.presence
    return head :bad_request unless payload

    begin
      json_payload = JSON.parse(payload)
    rescue JSON::ParserError
      Rails.logger.error("[WebhooksController#api_box] Failed to parse JSON payload: #{payload}")
      return head :bad_request
    end

    task_id = extract_task_id(json_payload)
    unless task_id
      Rails.logger.error("[WebhooksController#api_box] Task ID not found in payload: #{json_payload}")
      return head :bad_request # Task ID is essential
    end

    task = GenerationTask.find_by(task_id: task_id)
    unless task
      Rails.logger.warn("[WebhooksController#api_box] Task not found for task_id: #{task_id}. Payload: #{json_payload}")
      # Consider returning :ok if the API provider retries on :not_found,
      # to prevent repeated processing of an already handled or irrelevant task_id.
      return head :not_found
    end

    callback_type = extract_callback_type(json_payload)
    unless callback_type
      Rails.logger.warn("[WebhooksController#api_box] Callback type not found for task #{task.id}. Payload: #{json_payload}")
      return head :bad_request # Callback type is essential
    end

    begin
      # process_webhook_callback now raises an error on failure or returns true.
      task.process_webhook_callback(callback_type, json_payload)
      head :ok
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error("[WebhooksController#api_box] Validation failed for task #{task.id} processing callback '#{callback_type}': #{e.message}. Errors: #{e.record.errors.full_messages.join(", ")}")
      head :unprocessable_entity
    rescue ArgumentError => e
      Rails.logger.warn("[WebhooksController#api_box] Invalid argument or unknown callback type for task #{task.id}: #{e.message}")
      head :unprocessable_entity
    rescue => e
      Rails.logger.error("[WebhooksController#api_box] Unexpected error for task #{task.id} (type: '#{callback_type}'): #{e.message} \n#{e.backtrace.join("\n")}")
      head :internal_server_error
    end
  end

  private

  def extract_task_id(json_payload)
    json_payload.dig("data", "task_id") || json_payload["task_id"] # Prioritize nested, then top-level
  end

  def extract_callback_type(json_payload)
    json_payload.dig("data", "callbackType") || json_payload["callbackType"]
  end

  # === PRIMARY PAYMENT PROCESSOR METHODS ===

  # Handle successful checkout session - MAIN payment processor
  def handle_checkout_session_completed(session)
    order = Order.find_by(stripe_session_id: session["id"])
    return Rails.logger.warn "Order not found for session: #{session['id']}" unless order

    if session["payment_status"] == "paid"
      order.mark_as_paid!(payment_intent_id: session["payment_intent"])
    else
      Rails.logger.warn "Session completed but payment not paid: #{session['id']}"
    end
  rescue => e
    Rails.logger.error "Error processing checkout session #{session['id']}: #{e.message}"
    order&.mark_as_failed!(reason: e.message)
  end

  def handle_payment_intent_succeeded(payment_intent)
    # Additional handling for payment intent if needed
    Rails.logger.info "[WebhooksController#stripe] Payment intent succeeded: #{payment_intent['id']}"
  end

  def handle_payment_intent_failed(payment_intent)
    # Handle failed payments
    Rails.logger.info "[WebhooksController#stripe] Payment intent failed: #{payment_intent['id']}"

    # Find order by payment intent and mark as failed
    Order.joins(:metadata).where("metadata->>'payment_intent_id' = ?", payment_intent["id"]).each do |order|
      order.mark_as_failed!(reason: payment_intent["last_payment_error"]&.dig("message"))
    end
  end
end
