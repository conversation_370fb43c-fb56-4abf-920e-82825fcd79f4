class PricingController < ApplicationController
  allow_unauthenticated_access
  before_action :set_current_user_if_authenticated

  def index
    @monthly_plans = Plan.subscription_plans.monthly.active
    @yearly_plans = Plan.subscription_plans.yearly.active
  end

  def credits
    @credit_packages = Plan.credit_packages.active.by_price
  end

  private

  def set_current_user_if_authenticated
    resume_session if cookies.signed[:session_id]
  end
end
