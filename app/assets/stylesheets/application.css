/*
 * This is a manifest file that'll be compiled into application.css.
 *
 * With Propshaft, assets are served efficiently without preprocessing steps. You can still include
 * application-wide styles in this file, but keep in mind that CSS precedence will follow the standard
 * cascading order, meaning styles declared later in the document or manifest will override earlier ones,
 * depending on specificity.
 *
 * Consider organizing styles into separate files for maintainability.
 */

/* ========================================
   Audio Player Styles
   ======================================== */

/* Player Container */
.audio-player-container {
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  border-left: 1px solid rgb(71, 85, 105);
  border-right: 1px solid rgb(71, 85, 105);
  box-shadow: 0 -10px 40px -10px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 100;
  transition: all 0.3s ease;
}

/* Animated top border */
.audio-player-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent,
    rgba(99, 102, 241, 0.5) 20%,
    rgba(168, 85, 247, 0.5) 50%,
    rgba(99, 102, 241, 0.5) 80%,
    transparent
  );
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* Song Information */
.song-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.album-art {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: transform 0.2s ease;
  overflow: hidden;
}

.album-art:hover {
  transform: scale(1.05);
}

.album-art img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.album-art svg.default-icon {
  width: 24px;
  height: 24px;
  color: white;
  opacity: 0.9;
}

.song-details {
  flex: 1;
  min-width: 0;
}

.song-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 0.125rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.song-artist {
  font-size: 0.75rem;
  color: #94a3b8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Player Controls */
.custom-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

/* Play/Pause Button */
.play-button {
  width: 48px;
  height: 48px;
  min-width: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.6s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  position: relative;
  overflow: hidden;
}

.play-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0);
  transition: background 0.2s ease;
}

.play-button:hover::before {
  background: rgba(255, 255, 255, 0.1);
}

.play-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.play-button:active {
  transform: scale(0.95);
}

.play-button svg {
  width: 20px;
  height: 20px;
  color: white;
  position: relative;
  z-index: 1;
}

.play-button .play-icon {
  margin-left: 2px; /* Visual centering */
}

/* Progress Bar */
.progress-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 0;
}

.progress-bar-container {
  position: relative;
  height: 6px;
  background: rgba(71, 85, 105, 0.5);
  border-radius: 3px;
  cursor: pointer;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.progress-bar-container:hover {
  transform: scaleY(1.5);
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  position: relative;
  transition: width 0.1s ease;
  will-change: width;
}

.progress-bar::after {
  content: '';
  position: absolute;
  right: -7px;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.progress-bar-container:hover .progress-bar::after {
  opacity: 1;
}

/* Streaming Progress Animation */
.progress-bar.streaming-progress {
  background: linear-gradient(90deg,
    rgba(102, 126, 234, 0.6) 0%,
    rgba(102, 126, 234, 1) 50%,
    rgba(102, 126, 234, 0.6) 100%
  );
  background-size: 200% 100%;
  animation: streaming-progress 2s ease-in-out infinite;
}

@keyframes streaming-progress {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: 0% 0; }
}

/* Streaming Play Button Animation */
.streaming-play-button {
  animation: streaming-pulse 2s ease-in-out infinite;
}

.streaming-play-button:hover {
  animation: none;
  background: rgba(0, 0, 0, 0.5) !important;
}

@keyframes streaming-pulse {
  0%, 100% {
    background: rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
  }
  50% {
    background: rgba(0, 0, 0, 0.4);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
  }
}

.time-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #94a3b8;
  font-variant-numeric: tabular-nums;
}

/* Volume Control */
.volume-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.volume-button {
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.volume-button:hover {
  color: #e2e8f0;
  background: rgba(71, 85, 105, 0.3);
}

.volume-button svg {
  width: 20px;
  height: 20px;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(71, 85, 105, 0.5);
  border-radius: 2px;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.volume-slider:hover {
  transform: scaleY(1.5);
}

.volume-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  transition: width 0.1s ease;
  will-change: width;
}

/* Control Buttons */
.control-button {
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.control-button:hover {
  color: #e2e8f0;
  background: rgba(71, 85, 105, 0.3);
}

.control-button svg {
  width: 18px;
  height: 18px;
}

/* Player States */
.audio-player-container.loading .play-button {
  opacity: 0.5;
  cursor: wait;
}

.audio-player-container.loading .play-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f1f5f9;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error Messages */
.audio-error-message {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(239, 68, 68, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

.audio-error-message.show {
  opacity: 1;
}

/* Safari Support */
@supports (-webkit-touch-callout: none) {
  .audio-player-container {
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  .custom-audio-element {
    -webkit-playsinline: true;
  }

  /* Safari: Ensure proper touch handling */
  .control-button, .play-button, .volume-button {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
  }

  /* Safari: Fix potential layout issues */
  .progress-bar-container, .volume-slider {
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }
}

/* Responsive Tag Truncation */
.truncate-tags {
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive max-widths for different screen sizes */
@media (max-width: 640px) {
  .truncate-tags {
    max-width: 120px;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .truncate-tags {
    max-width: 150px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .truncate-tags {
    max-width: 180px;
  }
}

@media (min-width: 1025px) {
  .truncate-tags {
    max-width: 200px;
  }
}

audio.custom-audio-element {
  display: none;
}

/* Responsive Design */
@media (max-width: 640px) {
  .album-art {
    width: 40px;
    height: 40px;
  }

  .play-button {
    width: 40px;
    height: 40px;
    min-width: 40px;
  }

  .volume-control {
    display: none;
  }

  .song-info {
    gap: 0.75rem;
  }

  .custom-controls {
    gap: 0.75rem;
  }
}

/* ========================================
   Custom Scrollbar Styles
   ======================================== */

/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.4);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.6);
}

::-webkit-scrollbar-thumb:active {
  background: rgba(148, 163, 184, 0.8);
}

/* For thin scrollbars in specific containers */
.thin-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.05);
  border-radius: 3px;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.4) rgba(71, 85, 105, 0.1);
}

/* Enhanced scrollbar for main content areas */
.main-scrollable {
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.3) transparent;
}

.main-scrollable::-webkit-scrollbar {
  width: 6px;
}

.main-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.main-scrollable::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
  transition: all 0.2s ease;
}

.main-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
  width: 8px;
}

/* ========================================
   Hero Section Animations
   ======================================== */

/* Hero Subtitle Styles */
.hero-subtitle {
  opacity: 0;
  transform: translateY(30px);
  animation: hero-subtitle-appear 1.2s ease-out 0.5s forwards;
}

.hero-subtitle-container {
  display: block;
  position: relative;
}

.hero-subtitle-text {
  display: inline;
  color: transparent;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 25%, #f8fafc 50%, #e2e8f0 75%, #f1f5f9 100%);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  animation: hero-fade-in 1.2s ease-out 0.5s forwards,
             hero-subtitle-shimmer 4s ease-in-out 1.7s infinite;
  opacity: 0;
}

/* Floating accent elements */
.hero-accent {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  animation: hero-accent-float 2s ease-out forwards;
}

.hero-accent-1 {
  top: -16px;
  left: -16px;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #a855f7, #ec4899);
  animation-delay: 2s;
}

.hero-accent-2 {
  bottom: -8px;
  right: -8px;
  width: 4px;
  height: 4px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  animation-delay: 2.5s;
}

.hero-accent-3 {
  top: 50%;
  left: -32px;
  width: 4px;
  height: 32px;
  border-radius: 2px;
  background: linear-gradient(180deg, transparent, rgba(147, 51, 234, 0.3), transparent);
  animation-delay: 3s;
}

/* Keyframe Animations */
@keyframes hero-subtitle-appear {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes hero-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes hero-subtitle-shimmer {
  0%, 100% {
    background-position: 200% 200%;
  }
  50% {
    background-position: 0% 0%;
  }
}



@keyframes hero-accent-float {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.5);
  }
  to {
    opacity: 0.6;
    transform: translateY(0) scale(1);
  }
}

@media (prefers-reduced-motion: reduce) {
  .hero-subtitle {
    animation: hero-subtitle-appear 0.6s ease-out forwards;
  }

  .hero-subtitle-text {
    animation: hero-fade-in 0.6s ease-out 0.5s forwards;
  }

  .hero-accent {
    animation: none;
    opacity: 0.4;
    transform: none;
  }
}
