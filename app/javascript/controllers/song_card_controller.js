import { Controller } from "@hotwired/stimulus"

// Handles song card interactions:
// - Click on card -> handled by Turbo Frame link
// - Click on play button -> plays audio via global audio player
export default class extends Controller {
  static targets = ["card", "playButton", "playIcon", "pauseIcon"]
  static values = {
    id: Number,
    audioUrl: String,
    songTitle: String,
    imageUrl: String,
    aiGenerated: String
  }

  connect() {
    this.isPlaying = false
    this.boundSyncWithPlayer = this.syncWithPlayer.bind(this)
    this.boundHandlePlayerStateChange = this.handlePlayerStateChange.bind(this)

    // Listen for global player state changes
    this.setupPlayerSync()
  }

  disconnect() {
    this.removePlayerSync()
  }

  setupPlayerSync() {
    // Listen for audio events from the global player
    const audioPlayer = document.querySelector('[data-controller*="audio-player"][data-audio-player-mode-value="global"]')
    const audioElement = audioPlayer?.querySelector('audio')

    if (audioElement) {
      audioElement.addEventListener('play', this.boundSyncWithPlayer)
      audioElement.addEventListener('pause', this.boundSyncWithPlayer)
    }

    // Listen for player state broadcasts
    document.addEventListener('playerStateChanged', this.boundHandlePlayerStateChange)
  }

  removePlayerSync() {
    const audioPlayer = document.querySelector('[data-controller*="audio-player"][data-audio-player-mode-value="global"]')
    const audioElement = audioPlayer?.querySelector('audio')

    if (audioElement) {
      audioElement.removeEventListener('play', this.boundSyncWithPlayer)
      audioElement.removeEventListener('pause', this.boundSyncWithPlayer)
    }

    // Remove player state broadcast listener
    document.removeEventListener('playerStateChanged', this.boundHandlePlayerStateChange)
  }

  syncWithPlayer() {
    const audioPlayer = document.querySelector('[data-controller*="audio-player"][data-audio-player-mode-value="global"]')
    const audioElement = audioPlayer?.querySelector('audio')

    if (audioElement && audioElement.src === this.audioUrlValue) {
      // This card's song is currently loaded - sync the state
      this.isPlaying = !audioElement.paused
      this.updatePlayButton()
    }
  }

  handlePlayerStateChange(event) {
    const { isPlaying, currentSrc, hasEnded } = event.detail

    if (hasEnded || currentSrc !== this.audioUrlValue) {
      // Song ended or different song loaded - reset this card to play state
      this.isPlaying = false
      this.updatePlayButton()
    } else if (currentSrc === this.audioUrlValue) {
      // This card's song state changed
      this.isPlaying = isPlaying
      this.updatePlayButton()
    }
  }

  // Handle song selection with brief visual feedback
  selectSong(event) {
    if (!this.hasCardTarget) return

    const card = this.cardTarget
    card.classList.add('ring-2', 'ring-blue-500', 'border-blue-500')
    setTimeout(() => card.classList.remove('ring-2', 'ring-blue-500', 'border-blue-500'), 800)
  }

  // Play audio via global audio player
  playAudio(event) {
    event.preventDefault()
    event.stopPropagation()

    if (!this.audioUrlValue) return

    // Find the global audio player
    const audioPlayer = document.querySelector('[data-controller*="audio-player"][data-audio-player-mode-value="global"]')
    const audioElement = audioPlayer?.querySelector('audio')

    if (this.isPlaying) {
      // Currently playing - pause it
      this.isPlaying = false
      this.updatePlayButton()

      if (audioElement) {
        audioElement.pause()
      }
    } else {
      // Currently paused - resume or play
      this.isPlaying = true
      this.updatePlayButton()

      // Check if this is the same song that's currently loaded
      const isSameSong = audioElement?.src === this.audioUrlValue

      if (isSameSong) {
        // Same song - just resume playback
        audioElement.play().catch(error => {
          console.error("Error resuming audio:", error)
        })
      } else {
        // Different song - load new song
        document.dispatchEvent(new CustomEvent('loadSong', {
          detail: {
            audioUrl: this.audioUrlValue,
            title: this.songTitleValue,
            artist: this.aiGeneratedValue,
            imageUrl: this.imageUrlValue,
            userInitiated: true
          }
        }))
      }
    }

    // Brief play button animation
    this.animatePlayButton()
  }

  updatePlayButton() {
    if (this.hasPlayIconTarget && this.hasPauseIconTarget) {
      this.playIconTarget.classList.toggle('hidden', this.isPlaying)
      this.pauseIconTarget.classList.toggle('hidden', !this.isPlaying)
    }
  }

  animatePlayButton() {
    if (!this.hasPlayButtonTarget) return

    this.playButtonTarget.classList.add('scale-110')
    setTimeout(() => this.playButtonTarget.classList.remove('scale-110'), 200)
  }
}
