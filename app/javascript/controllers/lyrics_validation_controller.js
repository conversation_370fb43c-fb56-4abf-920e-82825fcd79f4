import { Controller } from "@hotwired/stimulus"

// to validate title cant be empty
export default class extends Controller {
  static targets = ["titleInput", "titleError"]

  validateForm(event) {
    // Check if we're in lyrics mode
    const lyricsContent = document.getElementById("lyrics-mode-content")
    const isLyricsMode = lyricsContent && !lyricsContent.classList.contains("hidden")

    if (isLyricsMode) {
      const titleInput = this.titleInputTarget
      const titleValue = titleInput.value.trim()

      if (titleValue === "") {
        event.preventDefault()
        this.showError(titleInput)
        return false
      }
    }

    return true
  }

  showError(titleInput) {
    const errorDiv = this.titleErrorTarget

    if (errorDiv) errorDiv.classList.remove("hidden")
    if (titleInput) {
      titleInput.classList.add("border-red-500")
      titleInput.classList.remove("border-slate-600")
      titleInput.focus()
    }
  }

  hideError() {
    const errorDiv = this.titleErrorTarget
    const titleInput = this.titleInputTarget

    if (errorDiv) errorDiv.classList.add("hidden")
    if (titleInput) {
      titleInput.classList.remove("border-red-500")
      titleInput.classList.add("border-slate-600")
    }
  }

  connect() {
    // Add input listener to hide error when user types
    const titleInput = this.titleInputTarget
    if (titleInput) {
      titleInput.addEventListener("input", () => {
        if (titleInput.value.trim() !== "") {
          this.hideError()
        }
      })
    }
  }
}
