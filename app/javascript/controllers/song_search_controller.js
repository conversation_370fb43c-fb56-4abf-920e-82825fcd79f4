import { Controller } from "@hotwired/stimulus"

// Standard search controller - input field stays in place
export default class extends Controller {
  static targets = ["input", "form", "results"]

  // Search as user types with debounce
  search(event) {
    // Clear existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout)
    }

    // Debounce search
    this.searchTimeout = setTimeout(() => {
      this.formTarget.requestSubmit()
    }, 300)
  }
}
