import { Controller } from "@hotwired/stimulus";

// Responsibilities:
// 1.  <PERSON><PERSON> pills for selected tags.
// 2.  Keep a Set (this.tags) in sync with hidden <input> elements that will be submitted with the form.
// 3.  Update a Turbo Frame's src to fetch suggestions as the user types.
// 4.  Provide quick-add buttons.


export default class extends Controller {
  static targets = [
    "searchInput",
    "suggestionsDropdown",
    "selectedTagsContainer",
    "quickAddContainer",
    "hiddenInputsContainer"
  ];

  static values = {
    inputName: String,
    category: String,
    originalPlaceholder: String,
    locale: String,
  };

  connect() {
    // Fresh state
    this.tags = new Set();
    this._refreshPlaceholder();
    this._updateQuickAddVisibility();

    // Handle form reset
    const form = this.element.closest("form");
    if (form) form.addEventListener("reset", () => setTimeout(() => this.reset()));

    // index for keyboard navigation through suggestions
    this.selectedIndex = -1;
  }
  /* =====================================================
   *  data-action methods
   * =================================================== */
  handleInput() {
    clearTimeout(this.timeout);

    const query = this.searchInputTarget.value.trim();

    this.suggestionsDropdownTarget.classList.toggle("hidden", query.length === 0);

    this.timeout = setTimeout(() => {
      const currentQuery = this.searchInputTarget.value.trim();
      if (!currentQuery) {
        this._hideDropdown();
        return;
      }

      if (this.hasSuggestionsDropdownTarget) {
        const params = new URLSearchParams();
        params.append("query", currentQuery);
        params.append("target_id", this.suggestionsDropdownTarget.id);
        if (this.hasCategoryValue) params.append("category", this.categoryValue);

        const url = `/${this.localeValue}/genres?${params.toString()}`;
        this.suggestionsDropdownTarget.src = url;
      }
    }, 100);

    // reset keyboard selection immediately
    this.selectedIndex = -1;
  }

  addTag(e) { this._addTag(e.currentTarget.dataset.tagValue); }

  addTagByValue(value) {
    this._addTag(value);
  }

  handleBackspace(e) {
    if (e.key !== "Backspace" || e.target.value) return;

    const pills = Array.from(this.selectedTagsContainerTarget.querySelectorAll('span[data-tag-value]'));
    if (!pills.length) return;

    const tagToRemove = pills[pills.length - 1].dataset.tagValue;
    this._removeTag(tagToRemove);
  }

  /* =====================================================
   *  Core helpers
   * =================================================== */
  reset() {
    this.tags = new Set();
    // Remove all pills
    Array.from(this.selectedTagsContainerTarget.querySelectorAll('span[data-tag-value]')).forEach(el=>el.remove());
    this._refreshPlaceholder();
    this._hideDropdown();
    this._updateQuickAddVisibility();
  }

  /* =====================================================
   *  Private – adding & removing tags
   * =================================================== */
  _addTag(raw) {
    const tag = raw?.trim();
    if (!tag) return;
    if (this.tags.has(tag.toLowerCase())) return; // Skip duplicates.

    this.tags.add(tag.toLowerCase());
    this._createHiddenInput(tag);
    this._createPill(tag);
    this._clearSearch();
    this._updateQuickAddVisibility();
  }

  _removeTag(tag) {
    if (!tag) return;
    // Remove pill element
    const pill = Array.from(this.selectedTagsContainerTarget.querySelectorAll('span[data-tag-value]'))
      .find(el => el.dataset.tagValue === tag);
    pill?.remove();

    // Remove hidden input if present
    this._hiddenInputFor(tag)?.remove();

    // Update Set
    this.tags.delete(tag.toLowerCase());

    // UI updates
    this._refreshPlaceholder();
    this._updateQuickAddVisibility();
  }

  /* =====================================================
   *  DOM helpers
   * =================================================== */
  _createPill(tag) {
    const pill = document.createElement("span");
    pill.className = "inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-600 text-white";
    pill.dataset.tagValue = tag;

    pill.append(tag);

    const btn = document.createElement("button");
    btn.type = "button";
    btn.innerHTML = "&times;";
    btn.dataset.action = "click->searchable-tag-input#removeTag";
    btn.dataset.tagValue = tag;
    btn.className = "-me-0.5 ms-1.5 inline-flex text-blue-100 hover:text-white focus:outline-none cursor-pointer";

    pill.append(btn);
    this.selectedTagsContainerTarget.insertBefore(pill, this.searchInputTarget);
    this._refreshPlaceholder();
  }

  /* =====================================================
   *  UI helpers
   * =================================================== */

  _refreshPlaceholder() {
    this.searchInputTarget.placeholder = this.tags.size ? "" : this.originalPlaceholderValue;
  }

  _hideDropdown() {
    this.suggestionsDropdownTarget.innerHTML = "";
    this.suggestionsDropdownTarget.classList.add("hidden");
    this.selectedIndex = -1;
  }

  _clearSearch() {
    this.searchInputTarget.value = "";
    this.handleInput(); // Will clear suggestions
  }

  /* =====================================================
   *  Action for remove tag
   * =================================================== */
  removeTag(event) {
    const tag = event.currentTarget.dataset.tagValue;
    this._removeTag(tag);
  }

  /* ==============================================
   * Keyboard interactions on the search input
   * data-action keydown-> methods are wired in ERB
   * ============================================ */

  navigateSuggestions(e) {
    if (!['ArrowDown', 'ArrowUp'].includes(e.key)) return;

    const items = this._suggestionItems();
    if (!items.length) return;

    e.preventDefault();
    if (e.key === 'ArrowDown') {
      this.selectedIndex = (this.selectedIndex + 1) % items.length;
    } else {
      this.selectedIndex = (this.selectedIndex - 1 + items.length) % items.length;
    }
    this._highlightSuggestion(items);
  }

  addTagFromInputOrSelect(e) {
    if (e.key !== 'Enter') return;
    e.preventDefault();

    const items = this._suggestionItems();
    if (items[this.selectedIndex]) {
      this._addTag(items[this.selectedIndex].dataset.tagValue);
    } else {
      const raw = this.searchInputTarget.value.trim();
      if (raw) this._addTag(raw);
    }
  }

  /* helper to fetch current suggestion DOM nodes */
  _suggestionItems() {
    return Array.from(this.suggestionsDropdownTarget.querySelectorAll('.suggestion-item'));
  }

  _highlightSuggestion(items) {
    items.forEach((el, idx) => {
      const active = idx === this.selectedIndex;
      el.classList.toggle('bg-slate-600', active);
      el.classList.toggle('text-white', active);
      if (!active) {
        el.classList.add('text-slate-200');
        el.classList.remove('text-white');
      }
      if (active) el.scrollIntoView({ block: 'nearest' });
    });
  }

  /* =====================================================
   *  Hidden input helpers (keep tags in main form submission)
   * =================================================== */
  _createHiddenInput(tag) {
    if (!this.hasHiddenInputsContainerTarget) return;

    const input = document.createElement("input");
    input.type  = "hidden";
    input.name  = this.inputNameValue;
    input.value = tag;
    this.hiddenInputsContainerTarget.appendChild(input);
  }

  _hiddenInputFor(tag) {
    if (!this.hasHiddenInputsContainerTarget) return null;
    const escaped = tag.replace(/"/g, '\\"');
    return this.hiddenInputsContainerTarget.querySelector(`input[type='hidden'][name='${this.inputNameValue}'][value="${escaped}"]`);
  }

  /* =====================================================
   *  Quick add validation - hide buttons for selected tags
   * =================================================== */
  _updateQuickAddVisibility() {
    if (!this.hasQuickAddContainerTarget) return;

    const quickAddButtons = this.quickAddContainerTarget.querySelectorAll('button[data-tag-value]');

    quickAddButtons.forEach(button => {
      const tagValue = button.dataset.tagValue?.toLowerCase();
      const isSelected = this.tags.has(tagValue);

      button.style.display = isSelected ? 'none' : 'inline-block';
    });
  }

  clearTags() {
    // Create a copy of the tags to iterate over, as _removeTag modifies the original set
    [...this.tags].forEach(tag => this._removeTag(tag));
  }
}
