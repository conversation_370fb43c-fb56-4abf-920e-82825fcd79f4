import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["planCard"]
  static values = {
    currentPlanType: String,
    currentBillingInterval: String,
    currentTier: Number
  }

  connect() {
    this.preventSubmitHandler = (e) => e.preventDefault()
    this.updatePlanStates()
  }

  updatePlanStates() {
    this.planCardTargets.forEach((card, index) => {
      const planType = card.dataset.planType
      const billingInterval = card.dataset.billingInterval
      const tier = parseInt(card.dataset.tier)
      const button = card.querySelector('input[type="submit"], .plan-button')
      const form = card.querySelector('form')

      if (!button) return

      const planState = this.getPlanState(planType, billingInterval, tier, card)

      // Update button text and state
      button.value = planState.buttonText
      button.disabled = planState.disabled

      // Update button classes
      if (planState.disabled) {
        button.classList.remove('bg-blue-700', 'hover:bg-blue-800', 'dark:bg-blue-600', 'dark:hover:bg-blue-700')
        button.classList.add('bg-gray-400', 'cursor-not-allowed')
      } else {
        button.classList.remove('bg-gray-400', 'cursor-not-allowed')
        button.classList.add('bg-blue-700', 'hover:bg-blue-800', 'dark:bg-blue-600', 'dark:hover:bg-blue-700')
      }

      // Handle tooltips
      this.handleTooltip(button, card, planState.tooltip, index)

      // Disable form submission for disabled plans
      if (planState.disabled && form) {
        form.removeEventListener('submit', this.preventSubmitHandler)
        form.addEventListener('submit', this.preventSubmitHandler)
      }
    })
  }

  handleTooltip(button, card, tooltipText, index) {
    const tooltipId = `plan-tooltip-${index}`

    // Remove existing tooltip
    const existingTooltip = document.getElementById(tooltipId)
    if (existingTooltip) {
      existingTooltip.remove()
      button.removeAttribute('data-tooltip-target')
    }

    // Add new tooltip if needed
    if (tooltipText) {
      button.setAttribute('data-tooltip-target', tooltipId)

      const tooltipElement = document.createElement('div')
      tooltipElement.id = tooltipId
      tooltipElement.role = 'tooltip'
      tooltipElement.className = 'absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-xs opacity-0 tooltip dark:bg-gray-700'
      tooltipElement.textContent = tooltipText;
      const arrowElement = document.createElement('div');
      arrowElement.className = 'tooltip-arrow';
      arrowElement.setAttribute('data-popper-arrow', '');
      tooltipElement.appendChild(arrowElement);

      // Append tooltip to card for proper positioning
      card.style.position = 'relative'
      card.appendChild(tooltipElement)
    }
  }

  getPlanState(planType, billingInterval, tier, card) {
    const currentType = this.currentPlanTypeValue
    const currentInterval = this.currentBillingIntervalValue

    // Current plan
    if (planType === currentType && billingInterval === currentInterval) {
      return {
        buttonText: card.dataset.textCurrentPlan || "Current Plan",
        disabled: true,
        tooltip: card.dataset.tooltipAlreadySubscribed || "You're already on this plan"
      }
    }

    // Free plan users can choose any plan
    if (currentType === 'free') {
      return {
        buttonText: card.dataset.textChoosePlan || "Choose Plan",
        disabled: false,
        tooltip: null
      }
    }

    // Check if it's a valid upgrade
    const isUpgrade = this.isValidUpgrade(tier, billingInterval)

    if (isUpgrade) {
      return {
        buttonText: card.dataset.textUpgrade || "Upgrade",
        disabled: false,
        tooltip: null
      }
    } else {
      return {
        buttonText: card.dataset.textChoosePlan || "Choose Plan",
        disabled: true,
        tooltip: card.dataset.tooltipCantSelect || "You can't select this plan"
      }
    }
  }

  isValidUpgrade(planTier, billingInterval) {
    const currentTier = this.currentTierValue
    const currentInterval = this.currentBillingIntervalValue

    // Same billing interval: must be higher tier
    if (billingInterval === currentInterval) {
      return planTier > currentTier
    }

    // Monthly user can upgrade to yearly plans of same or higher tier
    if (currentInterval === 'month' && billingInterval === 'year') {
      return planTier >= currentTier
    }

    // Yearly users cannot downgrade to monthly
    return false
  }
}
