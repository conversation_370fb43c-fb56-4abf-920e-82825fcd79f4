import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["packagesContainer", "radioInput", "submitButton"]

  connect() {
    this.updateSubmitButton()
  }

  selectPackage(event) {
    // Remove selection from all packages
    this.packagesContainerTarget.querySelectorAll('.bg-purple-50, .dark\\:bg-purple-900\\/20').forEach(el => {
      el.classList.remove('ring-2', 'ring-blue-500', 'bg-purple-50', 'dark:bg-purple-900/20')
    })

    // Get the clicked package container
    const packageContainer = event.currentTarget
    const planId = packageContainer.dataset.planId

    // Find and check the radio button
    const radioInput = packageContainer.querySelector('input[type="radio"]')
    if (radioInput) {
      radioInput.checked = true

      // Add visual feedback
      packageContainer.classList.add('ring-2', 'ring-blue-500', 'bg-purple-50', 'dark:bg-purple-900/20')

      // Update submit button text
      this.updateSubmitButton()

      // Animate the selection
      this.animateSelection(packageContainer)
    }
  }

  updateSubmitButton() {
    const selectedRadio = this.radioInputTargets.find(input => input.checked)

    if (selectedRadio && this.hasSubmitButtonTarget) {
      this.submitButtonTarget.disabled = false
    }
  }

  animateSelection(element) {
    // Add a subtle animation
    element.style.transform = 'scale(1.02)'
    element.style.transition = 'transform 0.2s ease'

    setTimeout(() => {
      element.style.transform = 'scale(1)'
    }, 200)
  }
}
