import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="tabs"
export default class extends Controller {
  static targets = [ "descriptionTab", "lyricsTab", "descriptionContent", "lyricsContent" ];

  activeTabClasses = ['text-blue-500', 'bg-slate-700', 'active'];

  connect() {
    // default: description active
    this._activate(this.descriptionTabTarget, this.descriptionContentTarget);
  }

  changeTab(e) {
    const tab = e.currentTarget;
    const isDesc = tab === this.descriptionTabTarget;
    this._activate(
      isDesc ? this.descriptionTabTarget : this.lyricsTabTarget,
      isDesc ? this.descriptionContentTarget : this.lyricsContentTarget
    );
  }

  select(tabName) {
    const isDesc = tabName === 'description';
    this._activate(
      isDesc ? this.descriptionTabTarget : this.lyricsTabTarget,
      isDesc ? this.descriptionContentTarget : this.lyricsContentTarget
    );
  }

  /* helpers */
  _activate(tabEl, contentEl) {
    // reset all
    [this.descriptionTabTarget, this.lyricsTabTarget].forEach(t => {
      t.classList.remove(...this.activeTabClasses);
      t.removeAttribute('aria-current');
    });
    [this.descriptionContentTarget, this.lyricsContentTarget].forEach(c => c.classList.add('hidden'));

    // activate chosen
    tabEl.classList.add(...this.activeTabClasses);
    tabEl.setAttribute('aria-current', 'page');
    contentEl.classList.remove('hidden');

    // Set the custom_style_enabled hidden field based on active tab
    const isLyricsMode = tabEl === this.lyricsTabTarget;
    this._setCustomStyleEnabled(isLyricsMode);
  }

  _setCustomStyleEnabled(isEnabled) {
    // Find the form element - it should be a child of this.element
    const form = this.element.querySelector('form');
    if (!form) {
      console.error('[tabs_controller] Could not find form element');
      return;
    }

    // Find or create the hidden field inside the form
    let hiddenField = form.querySelector('input[name="generation_task[custom_style_enabled]"]');
    if (!hiddenField) {
      hiddenField = document.createElement('input');
      hiddenField.type = 'hidden';
      hiddenField.name = 'generation_task[custom_style_enabled]';
      form.appendChild(hiddenField);
      console.log('[tabs_controller] Created custom_style_enabled hidden field');
    }

    hiddenField.value = isEnabled ? '1' : '';
    console.log(`[tabs_controller] Set custom_style_enabled = '${hiddenField.value}' (isLyricsMode: ${isEnabled})`);
  }
}
