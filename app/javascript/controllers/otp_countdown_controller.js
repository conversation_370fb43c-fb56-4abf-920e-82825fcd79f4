import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["button"];
  static values = {
    expireIn: { type: Number, default: 60 },
    i18n: { type: Object, default: {} }
  };

  connect() {
    // Listen to the form submission event to restart the timer
    this.element.closest("form").addEventListener("turbo:submit-start", () => {
      this.startTimer();
    });

    this.startTimer();
  }

  disconnect() {
    this.clearTimer();
  }

  startTimer() {
    this.clearTimer();
    this.buttonTarget.disabled = true;
    this.buttonTarget.setAttribute("aria-live", "polite");

    let remainingTime = this.expireInValue;
    this.updateButtonText(remainingTime);

    this.timerId = setInterval(() => {
      remainingTime--;
      this.updateButtonText(remainingTime);
      if (remainingTime <= 0) {
        this.clearTimer();
      }
    }, 1000);
  }

  updateButtonText(remainingTime) {
    const isCountingDown = remainingTime > 0;
    this.buttonTarget.disabled = isCountingDown;

    if (isCountingDown) {
      this.buttonTarget.textContent = this.t("resendIn").replace("%{count}", remainingTime);
    } else {
      this.buttonTarget.textContent = this.t("resendCode");
    }
  }

  clearTimer() {
    if (this.timerId) {
      clearInterval(this.timerId);
      this.timerId = null;
    }
  }

  t(key) {
    return this.i18nValue[key] || key;
  }
}
