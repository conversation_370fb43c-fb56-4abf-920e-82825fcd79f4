import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [ "input", "count" ]
  static values = { limit: Number }

  connect() {
    this.updateCount()
    setTimeout(() => this.resize(), 50)
  }

  updateCount() {
    if (!this.hasInputTarget) {
      return
    }

    if (this.hasCountTarget) {
      const currentLength = this.inputTarget.value.length
      const limit = this.limitValue

      if (limit > 0) {
        this.countTarget.textContent = `${currentLength}/${limit}`
        this.countTarget.classList.toggle('text-red-500', currentLength > limit)
      } else {
        this.countTarget.textContent = ''
        this.countTarget.classList.remove('text-red-500')
      }
    }

    this.resize()
  }

  resize() {
    this.inputTarget.style.height = 'auto'
    const newHeight = this.inputTarget.scrollHeight
    if (newHeight > 0) {
      this.inputTarget.style.height = `${newHeight}px`
    }
  }
}

// For now, we treat "limit = 0" as unlimited; the counter element remains blank.
// If a limit is provided (positive number), the counter shows current/limit.
