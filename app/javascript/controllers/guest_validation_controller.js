import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["submitButton"]
  static values = { isLoggedIn: Boolean }

  connect() {
    if (!this.isLoggedInValue) {
      this.validateForm()
      this.bindFormEvents()
      this.startObserving()
    }
  }

  bindFormEvents() {
    // Monitor all form inputs for changes
    const formInputs = this.element.querySelectorAll('input, textarea, select')
    formInputs.forEach(input => {
      input.addEventListener('input', () => this.validateForm())
      input.addEventListener('change', () => this.validateForm())
    })
  }

  startObserving() {
    // Observe DOM changes to catch dynamically added hidden inputs from searchable tag inputs
    this.observer = new MutationObserver(() => {
      this.validateForm()
    })

    this.observer.observe(this.element, {
      childList: true,
      subtree: true
    })
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }

  validateForm() {
    if (this.isLoggedInValue) {
      this.submitButtonTarget.disabled = false
      return
    }

    // For non-logged users, check if any required fields have content
    const hasDescription = this.getInputValue('generation_task[description]').trim() !== ''
    const hasLyrics = this.getInputValue('generation_task[lyrics]').trim() !== ''
    const hasGenres = this.getHiddenInputValues('generation_task[genres][]').length > 0
    const hasMoods = this.getHiddenInputValues('generation_task[moods][]').length > 0
    const hasInstruments = this.getHiddenInputValues('generation_task[instruments][]').length > 0

    // Enable button if any of these conditions are met
    const hasAnyInput = hasDescription || hasLyrics || hasGenres || hasMoods || hasInstruments

    this.submitButtonTarget.disabled = !hasAnyInput

    // Update button appearance
    if (hasAnyInput) {
      this.submitButtonTarget.classList.remove('opacity-50', 'cursor-not-allowed')
      this.submitButtonTarget.classList.add('cursor-pointer')
    } else {
      this.submitButtonTarget.classList.add('opacity-50', 'cursor-not-allowed')
      this.submitButtonTarget.classList.remove('cursor-pointer')
    }
  }

  getInputValue(name) {
    const input = this.element.querySelector(`[name="${name}"]`)
    return input ? input.value : ''
  }

  getHiddenInputValues(name) {
    const inputs = this.element.querySelectorAll(`input[type="hidden"][name="${name}"]`)
    return Array.from(inputs).map(input => input.value).filter(value => value.trim() !== '')
  }
}
