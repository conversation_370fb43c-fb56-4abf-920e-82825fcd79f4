import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static values = {
    storageKey: { type: String, default: 'form-persistence' }
  }

  connect() {
    this.restoreState()
  }

  saveState() {
    const formData = new FormData(this.element)
    formData.delete('authenticity_token') // Don't need to persist this

    const params = new URLSearchParams(formData)

    // Find and add the active tab to the params
    const tabsControllerElement = this.element.closest('[data-controller="tabs"]')
    if (tabsControllerElement) {
      const activeTab = tabsControllerElement.querySelector('[aria-current="page"]')
      if (activeTab && activeTab.dataset.tabName) {
        params.set('active_tab', activeTab.dataset.tabName)
      }
    }

    const queryString = params.toString()

    console.log("--- Saving Form State (Query String) ---", queryString)
    sessionStorage.setItem(this.storageKeyValue, queryString)
  }

  restoreState() {
    const queryString = sessionStorage.getItem(this.storageKeyValue)
    if (!queryString) return

    console.log("--- Restoring Form State (Query String) ---", `?${queryString}`)
    const params = new URLSearchParams(queryString)

    // Defer execution to ensure other Stimulus controllers are connected
    requestAnimationFrame(() => {
      // Restore the active tab first
      const activeTabName = params.get('active_tab')
      if (activeTabName) {
        const tabsControllerElement = this.element.closest('[data-controller="tabs"]')
        const tabsController = this.application.getControllerForElementAndIdentifier(tabsControllerElement, 'tabs');
        if (tabsController) {
          tabsController.select(activeTabName)
        }
      }

      this.populateForm(params)
      sessionStorage.removeItem(this.storageKeyValue)

      // Automatically submit the form after restoring the state
      console.log("--- Submitting restored form ---")
      this.element.requestSubmit()
    })
  }

  populateForm(params) {
    const uniqueKeys = [...new Set([...params.keys()])];

    for (const key of uniqueKeys) {
      const values = params.getAll(key);
      const isArray = key.endsWith('[]')

      if (isArray) {
        this.restoreSearchableTags(key, values);
        continue;
      }

      const element = this.element.elements[key];
      if (!element) continue;

      const singleValue = values.length > 0 ? values[0] : null;

      if (element.constructor.name === 'RadioNodeList') {
        const controllerElement = element[0].closest('[data-controller="selectable-tag-group"]');
        if (controllerElement) {
          const controller = this.application.getControllerForElementAndIdentifier(controllerElement, 'selectable-tag-group');
          if (controller) {
            controller.selectValue(singleValue);
          }
        }
      } else {
        if (element.type === 'checkbox') {
          element.checked = singleValue === '1'
        } else {
          element.value = singleValue
        }
        element.dispatchEvent(new Event('input', { bubbles: true }))
        element.dispatchEvent(new Event('change', { bubbles: true }))
      }
    }
  }

  restoreSearchableTags(name, values) {
    const allSearchableControllers = this.application.controllers.filter(c => c.identifier === 'searchable-tag-input');
    const formControllers = allSearchableControllers.filter(c => this.element.contains(c.element));

    formControllers.forEach(controller => {
      if (controller.inputNameValue === name) {
        controller.clearTags()
        if (controller.element.offsetParent !== null) {
          values.forEach(value => {
            if (value) controller.addTagByValue(value)
          })
        }
      }
    });
  }
}
