import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["button", "menu", "chevron"]

  connect() {
    this.isOpen = false
    // Bind the close method to maintain context
    this.closeOnClickOutside = this.closeOnClickOutside.bind(this)
  }

  toggle(event) {
    event.preventDefault()
    event.stopPropagation()

    if (this.isOpen) {
      this.close()
    } else {
      this.open()
    }
  }

  open() {
    this.isOpen = true
    this.menuTarget.classList.remove("opacity-0", "invisible")
    this.menuTarget.classList.add("opacity-100", "visible")
    this.chevronTarget.style.transform = "rotate(180deg)"

    // Add click outside listener
    document.addEventListener("click", this.closeOnClickOutside)
  }

  close() {
    this.isOpen = false
    this.menuTarget.classList.remove("opacity-100", "visible")
    this.menuTarget.classList.add("opacity-0", "invisible")
    this.chevronTarget.style.transform = "rotate(0deg)"

    // Remove click outside listener
    document.removeEventListener("click", this.closeOnClickOutside)
  }

  closeOnClickOutside(event) {
    if (!this.element.contains(event.target)) {
      this.close()
    }
  }

  disconnect() {
    // Clean up event listener when controller is removed
    document.removeEventListener("click", this.closeOnClickOutside)
  }
}
