import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["form", "targetIdInput"]
  static values = {
    surpriseMeUrl: String,
    lyricsUrl: String,
  }

  connect() {
    this.chunkBuffer = new Map()
    this._resetState()
    this.animationInterval = null
  }

  disconnect() {
    this.endStream()
  }

  generate(event) {
    event.preventDefault()
    if (this.isStreaming) return

    const { type } = event.params
    const config = {
      surprise_me: {
        url: this.surpriseMeUrlValue,
        textareaId: 'song_description',
      },
      generate_lyrics: {
        url: this.lyricsUrlValue,
        textareaId: 'song_lyrics',
      },
    }[type]

    if (!config) return

    const textarea = document.getElementById(config.textareaId)
    if (!textarea) return

    this._startStream(config.url, textarea, event.currentTarget)
  }

  _startStream(url, targetTextarea, button) {
    this._resetState(targetTextarea, button)
    this._createStreamingTarget()
    this._setupStreamingObserver()

    this.formTarget.action = url
    this.targetIdInputTarget.value = this.streamingTarget.id
    this.formTarget.requestSubmit()
  }

  endStream() {
    if (!this.isStreaming) return

    this.isStreaming = false
    this._stopLoadingAnimation()
    this._updateButtonState()
    this._removeStreamingTarget()

    if (this.contentObserver) {
      this.contentObserver.disconnect()
      this.contentObserver = null
    }
  }

  _setupStreamingObserver() {
    this.contentObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type !== 'childList') continue

        for (const node of mutation.addedNodes) {
          if (node.nodeType === Node.ELEMENT_NODE && node.hasAttribute('data-stream-payload')) {
            this._handleStreamPayload(JSON.parse(node.dataset.streamPayload))
          }
        }
      }
    })

    this.contentObserver.observe(this.streamingTarget, { childList: true })
  }

  _handleStreamPayload(payload) {
    if (payload.done) {
      this.endStream()
      return
    }

    this.chunkBuffer.set(payload.sequence, payload.content)
    this._processChunkBuffer()
  }

  _processChunkBuffer() {
    while (this.chunkBuffer.has(this.expectedSequence)) {
      const content = this.chunkBuffer.get(this.expectedSequence)
      this.chunkBuffer.delete(this.expectedSequence)

      if (content) {
        this.hasReceivedContent = true
        const processedContent = content.replace(/<br>/g, '\n')
        this.currentTextarea.value += processedContent
        this.currentTextarea.dispatchEvent(new Event('input', { bubbles: true }))
      }

      this.expectedSequence++
    }
  }

  _updateButtonState() {
    if (!this.currentButton) return

    const button = this.currentButton
    if (this.isStreaming) {
      button.disabled = true
      button.classList.add('opacity-50', 'cursor-not-allowed')
      if (!button.dataset.originalText) {
        button.dataset.originalText = button.textContent
      }
      this._startLoadingAnimation(button)
    } else {
      button.disabled = false
      button.classList.remove('opacity-50', 'cursor-not-allowed')
      if (button.dataset.originalText) {
        button.textContent = button.dataset.originalText
        delete button.dataset.originalText
      }
    }
  }

  _startLoadingAnimation(button) {
    let dotCount = 0
    const originalText = button.dataset.originalText

    this.animationInterval = setInterval(() => {
      dotCount = (dotCount % 3) + 1
      button.textContent = `${originalText} ${'.'.repeat(dotCount)}`
    }, 500)
  }

  _stopLoadingAnimation() {
    if (this.animationInterval) {
      clearInterval(this.animationInterval)
      this.animationInterval = null
    }
  }

  _resetState(textarea = null, button = null) {
    this.isStreaming = !!(textarea && button)
    this.currentTextarea = textarea
    this.currentButton = button
    if (this.currentTextarea) this.currentTextarea.value = ""
    this.hasReceivedContent = false
    this.expectedSequence = 0
    this.chunkBuffer.clear()
    this._updateButtonState()
  }

  _createStreamingTarget() {
    const streamingTarget = document.createElement('div')
    streamingTarget.id = `streaming_content_${Date.now()}`
    streamingTarget.style.display = 'none'
    document.body.appendChild(streamingTarget)
    this.streamingTarget = streamingTarget
  }

  _removeStreamingTarget() {
    if (this.streamingTarget) {
      this.streamingTarget.remove()
      this.streamingTarget = null
    }
  }

  getTargetTextarea() {
    const textarea = this.element.querySelector('textarea')
    if (textarea) return textarea

    return document.querySelector('#song_description, #song_lyrics')
  }
}
