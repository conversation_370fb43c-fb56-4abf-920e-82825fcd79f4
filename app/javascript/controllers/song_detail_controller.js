import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="song-detail"
export default class extends Controller {
  static targets = [
    "content",
    "skeleton",
    "songImage",
    "imagePlaceholder",
    "songTitle",
    "playButton",
    "playIcon",
    "pauseIcon",
    "playText"
  ]

  static values = {
    songId: String
  }

  connect() {
    this.isPlaying = false
    this.boundSyncWithPlayer = this.syncWithPlayer.bind(this)
    this.boundHandlePlayerStateChange = this.handlePlayerStateChange.bind(this)

    // Listen for global player state changes
    this.setupPlayerSync()

    // Check initial player status
    this.checkInitialPlayerStatus()
  }

  disconnect() {
    this.removePlayerSync()
  }

  setupPlayerSync() {
    // Listen for audio events from the global player
    const audioPlayer = document.querySelector('[data-controller*="audio-player"][data-audio-player-mode-value="global"]')
    const audioElement = audioPlayer?.querySelector('audio')

    if (audioElement) {
      audioElement.addEventListener('play', this.boundSyncWithPlayer)
      audioElement.addEventListener('pause', this.boundSyncWithPlayer)
    }

    // Listen for player state broadcasts
    document.addEventListener('playerStateChanged', this.boundHandlePlayerStateChange)
  }

  removePlayerSync() {
    const audioPlayer = document.querySelector('[data-controller*="audio-player"][data-audio-player-mode-value="global"]')
    const audioElement = audioPlayer?.querySelector('audio')

    if (audioElement) {
      audioElement.removeEventListener('play', this.boundSyncWithPlayer)
      audioElement.removeEventListener('pause', this.boundSyncWithPlayer)
    }

    // Remove player state broadcast listener
    document.removeEventListener('playerStateChanged', this.boundHandlePlayerStateChange)
  }

  syncWithPlayer() {
    const audioPlayer = document.querySelector('[data-controller*="audio-player"][data-audio-player-mode-value="global"]')
    const audioElement = audioPlayer?.querySelector('audio')
    const button = this.hasPlayButtonTarget ? this.playButtonTarget : null

    if (audioElement && button) {
      const audioUrl = button.dataset.audioUrl
      if (audioElement.src === audioUrl) {
        // This song is currently loaded - sync the state
        this.isPlaying = !audioElement.paused
        this.updatePlayButton()
      }
    }
  }

  checkInitialPlayerStatus() {
    // Check if this song is already playing when the detail card connects
    const audioPlayer = document.querySelector('[data-controller*="audio-player"][data-audio-player-mode-value="global"]')
    const audioElement = audioPlayer?.querySelector('audio')
    const button = this.hasPlayButtonTarget ? this.playButtonTarget : null

    if (audioElement && button) {
      const audioUrl = button.dataset.audioUrl
      if (audioElement.src === audioUrl && !audioElement.paused) {
        // This song is currently playing - update button state
        this.isPlaying = true
        this.updatePlayButton()
      }
    }
  }

  handlePlayerStateChange(event) {
    const { isPlaying, currentSrc, hasEnded } = event.detail
    const button = this.hasPlayButtonTarget ? this.playButtonTarget : null

    if (button) {
      const audioUrl = button.dataset.audioUrl

      if (hasEnded || currentSrc !== audioUrl) {
        // Song ended or different song loaded - reset this button to play state
        this.isPlaying = false
        this.updatePlayButton()
      } else if (currentSrc === audioUrl) {
        // This song's state changed
        this.isPlaying = isPlaying
        this.updatePlayButton()
      }
    }
  }

  // Play/pause functionality - integrates with global audio player
  playPause(event) {
    event.preventDefault()
    event.stopPropagation()

    const button = event.currentTarget
    const audioUrl = button.dataset.audioUrl
    const title = button.dataset.songTitle
    const imageUrl = button.dataset.songImageUrl

    if (audioUrl) {
      // Get the global audio player
      const audioPlayer = document.querySelector('[data-controller*="audio-player"][data-audio-player-mode-value="global"]')
      const audioElement = audioPlayer?.querySelector('audio')

      if (this.isPlaying) {
        // Currently playing - pause it
        this.isPlaying = false
        this.updatePlayButton()

        if (audioElement) {
          audioElement.pause()
        }
      } else {
        // Currently paused - resume or play
        this.isPlaying = true
        this.updatePlayButton()

        // Check if this is the same song that's currently loaded
        const isSameSong = audioElement?.src === audioUrl

        if (isSameSong) {
          // Same song - just resume playback
          audioElement.play().catch(error => {
            console.error("Error resuming audio:", error)
          })
        } else {
          // Different song - load new song
          document.dispatchEvent(new CustomEvent('loadSong', {
            detail: {
              audioUrl,
              title,
              imageUrl,
              userInitiated: true
            }
          }))
        }
      }
    }
  }

  updatePlayButton() {
    if (this.hasPlayIconTarget && this.hasPauseIconTarget) {
      this.playIconTarget.classList.toggle('hidden', this.isPlaying)
      this.pauseIconTarget.classList.toggle('hidden', !this.isPlaying)
    }

    if (this.hasPlayTextTarget) {
      const playText = this.playTextTarget.dataset.playTextValue || 'Play'
      const pauseText = this.playTextTarget.dataset.pauseTextValue || 'Pause'
      this.playTextTarget.textContent = this.isPlaying ? pauseText : playText
    }
  }
}
