import { Controller } from "@hotwired/stimulus";

// Here define the controller for the selectable tag group

export default class extends Controller {
  static targets = ["hiddenInput"];

  activeClasses = ['bg-blue-600', 'text-white'];
  inactiveClasses = ['bg-slate-600', 'text-slate-300', 'hover:bg-slate-500'];

  connect() {
    // Ensure a default is selected and visuals are correct
    this.selectDefault();
    // Listen for form reset so we can restore default selection
    if (this.element.closest('form')) {
      this.element.closest('form')
          .addEventListener('reset', this.handleFormReset.bind(this));
    }
  }

  handleFormReset() {
    setTimeout(() => this.selectDefault(), 100);
  }

  select(event) {
    const selectedInput = event.target;
    this._selectInput(selectedInput)
  }

  selectValue(value) {
    const inputToSelect = this.hiddenInputTargets.find(input => input.value === value);
    if (inputToSelect) {
      this._selectInput(inputToSelect);
    }
  }

  _selectInput(selectedInput) {
    this.hiddenInputTargets.forEach(input => {
      input.checked = (input === selectedInput);
    });
    this.syncLabelAppearance();
  }

  syncLabelAppearance() {
    // ensure only one button is active
    this.hiddenInputTargets.forEach(inp => {
      const label = this.element.querySelector(`label[for="${inp.id}"]`);
      if (label) {
        this.updateLabelVisuals(label, inp.checked);
      }
    });
  }

  updateLabelVisuals(label, isActive) {
    [...this.activeClasses, ...this.inactiveClasses].forEach(cls => label.classList.remove(cls));

    const classesToAdd = isActive ? this.activeClasses : this.inactiveClasses;
    classesToAdd.forEach(c => label.classList.add(c));
  }

  // --- helpers ---

  selectDefault() {
    // 1. Find the currently checked input (if any)
    let checkedInput = this.hiddenInputTargets.find(inp => inp.checked);

    // 2. If none are checked (edge-case), select the first one
    if (!checkedInput) {
      const defaultInput = this.hiddenInputTargets.find(inp => inp.dataset.default === 'true');
      checkedInput = defaultInput || this.hiddenInputTargets[0];
      if (checkedInput) checkedInput.checked = true;
    }

    // 3. Refresh label visuals for all inputs
    this.syncLabelAppearance();
  }
}
