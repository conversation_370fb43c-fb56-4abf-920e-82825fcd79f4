import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["trigger", "content", "icon"]

  toggle(event) {
    const trigger = event.currentTarget;
    const content = trigger.parentElement.querySelector('[data-accordion-target="content"]');
    const icon = trigger.querySelector('[data-accordion-target="icon"]');
    if (content.classList.contains('hidden')) {
      content.classList.remove('hidden');
      content.setAttribute('aria-hidden', 'false');
      trigger.setAttribute('aria-expanded', 'true');
      icon.style.transform = 'rotate(180deg)';
    } else {
      content.classList.add('hidden');
      content.setAttribute('aria-hidden', 'true');
      trigger.setAttribute('aria-expanded', 'false');
      icon.style.transform = 'rotate(0deg)';
  }
}
}
