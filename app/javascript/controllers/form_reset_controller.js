import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["form"];

  clearForm(e) {
    e?.preventDefault();

    // Clear persisted form state from sessionStorage
    this._clearPersistedState();

    // Reset the form
    this.formTarget?.reset();

    // Manually trigger change events on key checkboxes to ensure UI state is updated
    this._triggerChangeEvents();
  }

  _clearPersistedState() {
    // Clear the form persistence storage if it exists
    const formPersistenceKey = this.element.dataset.formPersistenceStorageKeyValue || 'form-persistence';
    sessionStorage.removeItem(formPersistenceKey);
  }

  _triggerChangeEvents() {
    // Find and trigger change events on instrumental and custom style checkboxes
    const instrumentalCheckbox = this.formTarget?.querySelector('input[name="generation_task[instrumental]"]');
    const customStyleCheckbox = this.formTarget?.querySelector('input[name="generation_task[use_custom_style]"]');

    if (instrumentalCheckbox) {
      instrumentalCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
    }

    if (customStyleCheckbox) {
      customStyleCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // Also trigger change events on any radio buttons to ensure proper sync
    const radioButtons = this.formTarget?.querySelectorAll('input[type="radio"]:checked');
    radioButtons?.forEach(radio => {
      radio.dispatchEvent(new Event('change', { bubbles: true }));
    });
  }
}
