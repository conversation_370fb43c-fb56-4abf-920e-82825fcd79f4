import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["submitButton", "introduction", "workspace", "songList", "fakeCardTemplate", "loadingOverlay", "playButtonOverlay", "descriptionInput", "loginPromptCardTemplate"]

  connect() {
    console.log("Fake song controller connected for guest user")

    // If this is a fake song card, start the 5-second timer
    if (this.hasLoadingOverlayTarget && this.hasPlayButtonOverlayTarget) {
      setTimeout(() => {
        this.loadingOverlayTarget.classList.add('hidden')
        this.playButtonOverlayTarget.classList.remove('hidden')
      }, 5000)
    }
  }

  showFakeWorkspace() {
    this.introductionTarget.classList.add("hidden")
    this.workspaceTarget.classList.remove("hidden")

    const fakeCard = this.fakeCardTemplateTarget.content.cloneNode(true)

    // Add the controller attribute to all cloned cards
    const cardElements = fakeCard.querySelectorAll('.fake-song-card')
    cardElements.forEach(cardElement => {
      cardElement.setAttribute('data-controller', 'fake-song')
    })

    this.songListTarget.prepend(fakeCard)

    // Trigger the timer manually for each card
    cardElements.forEach(cardElement => {
      setTimeout(() => {
        const loadingOverlay = cardElement?.querySelector('[data-fake-song-target="loadingOverlay"]')
        const playButtonOverlay = cardElement?.querySelector('[data-fake-song-target="playButtonOverlay"]')

        if (loadingOverlay && playButtonOverlay) {
          loadingOverlay.classList.add('hidden')
          playButtonOverlay.classList.remove('hidden')
        }
      }, 5000)
    })

    // Schedule the login prompt card to appear shortly after the animations
    setTimeout(() => {
      if (this.hasLoginPromptCardTemplateTarget) {
        const loginPromptCard = this.loginPromptCardTemplateTarget.content.cloneNode(true);
        this.songListTarget.appendChild(loginPromptCard);
      }
    }, 5000);
  }



}
