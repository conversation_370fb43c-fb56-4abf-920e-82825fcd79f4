import { Controller } from "@hotwired/stimulus";

// Handles UI interactions for the *Lyrics* tab
// 1. Toggles lyrics textarea disabled state when *Instrumental* is selected
// 2. Keeps the *Instrumental* checkbox and *Vocal* radio buttons in sync
export default class extends Controller {
  static targets = [
    "instrumentalCheckbox",
    "lyricsTextarea",
    "vocalGroup", // This is the wrapper div for the vocal selectable_tag_group
    "customStyleCheckbox",
    "styleTextareaContainer",
    "defaultComponents"
  ];

  // ---------------------------------------------------------------------------
  // Lifecycle callbacks
  // ---------------------------------------------------------------------------
  connect() {
    // Ensure the UI is in a consistent state when the controller is first loaded
    this._refreshUI();
    this._refreshCustomStyleUI();
  }

  // ---------------------------------------------------------------------------
  // Actions (defined via `data-action` in the view)
  // ---------------------------------------------------------------------------
  instrumentalToggled() {
    this._syncFromCheckbox();
    // After programmatically changing a radio button, we must tell the
    // child controller to update its visual state.
    this.vocalTagGroupController?.syncLabelAppearance();
  }

  vocalSelectionChanged() {
    this._syncFromVocal();
    // When the user clicks a vocal, the child controller handles its own
    // visual state, so no extra call is needed here.
  }

  customStyleToggled() {
    this._refreshCustomStyleUI();
  }

  get instrumentalRadio() {
    return this.vocalGroupTarget.querySelector("input[value='instrumental']");
  }

  // Getter to find the child selectable-tag-group controller instance
  get vocalTagGroupController() {
    if (!this.hasVocalGroupTarget) return null;
    return this.application.getControllerForElementAndIdentifier(this.vocalGroupTarget, "selectable-tag-group");
  }

  _syncFromCheckbox() {
    if (this.instrumentalCheckboxTarget.checked) {
      this._ensureInstrumentalSelected();
    } else if (this.instrumentalRadio?.checked) {
      this._chooseDefaultVocal();
    }

    this._refreshUI();
  }

  _syncFromVocal() {
    if (this.instrumentalRadio) {
      this.instrumentalCheckboxTarget.checked = this.instrumentalRadio.checked;
    }

    this._refreshUI();
  }

  // Guarantee the vocal radio selection reflects the *Instrumental* checkbox
  _ensureInstrumentalSelected() {
    if (this.instrumentalRadio && !this.instrumentalRadio.checked) {
      this.instrumentalRadio.checked = true;
    }
  }

  // Picks the first non-instrumental vocal option when *Instrumental* is switched off
  _chooseDefaultVocal() {
    const firstRadio = this.vocalGroupTarget.querySelector("input[type='radio']:not([value='instrumental'])");
    if (firstRadio) {
      firstRadio.checked = true;
    }
  }

  _refreshUI() {
    const disabled = this.instrumentalCheckboxTarget.checked;
    this.lyricsTextareaTarget.toggleAttribute("disabled", disabled);
    this.lyricsTextareaTarget.classList.toggle("opacity-60", disabled);
    this.lyricsTextareaTarget.classList.toggle("cursor-not-allowed", disabled);
  }

  _refreshCustomStyleUI() {
    const customStyleEnabled = this.customStyleCheckboxTarget.checked;

    if (customStyleEnabled) {
      this.styleTextareaContainerTarget.classList.remove("hidden");
      this.defaultComponentsTarget.classList.add("hidden");
    } else {
      this.styleTextareaContainerTarget.classList.add("hidden");
      this.defaultComponentsTarget.classList.remove("hidden");
    }
  }
}
