import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["glow"]

  connect() {
    this.handleMouseMove = this.handleMouseMove.bind(this)
    this.handleMouseEnter = this.handleMouseEnter.bind(this)
    this.handleMouseLeave = this.handleMouseLeave.bind(this)
    
    this.element.addEventListener("mousemove", this.handleMouseMove)
    this.element.addEventListener("mouseenter", this.handleMouseEnter)
    this.element.addEventListener("mouseleave", this.handleMouseLeave)
  }

  disconnect() {
    this.element.removeEventListener("mousemove", this.handleMouseMove)
    this.element.removeEventListener("mouseenter", this.handleMouseEnter)
    this.element.removeEventListener("mouseleave", this.handleMouseLeave)
  }

  handleMouseMove(event) {
    const rect = this.element.getBoundingClientRect()
    const x = ((event.clientX - rect.left) / rect.width) * 100
    const y = ((event.clientY - rect.top) / rect.height) * 100
    
    this.glowTarget.style.setProperty('--mouse-x', `${x}%`)
    this.glowTarget.style.setProperty('--mouse-y', `${y}%`)
  }

  handleMouseEnter() {
    this.glowTarget.style.opacity = '1'
  }

  handleMouseLeave() {
    this.glowTarget.style.opacity = '0'
  }
}