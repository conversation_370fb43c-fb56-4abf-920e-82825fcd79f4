import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["emailInput", "errorMessage", "submitButton"]

  connect() {
    this.validateEmail()
  }

  validateEmail() {
    const email = this.emailInputTarget.value.trim()
    const isValid = this.isValidEmail(email)
    
    this.updateInputState(isValid)
    this.updateErrorMessage(isValid, email)
    this.updateSubmitButton(isValid, email)
  }

  isValidEmail(email) {
    if (!email) return false
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  updateInputState(isValid) {
    const input = this.emailInputTarget
    
    if (!input.value.trim()) {
      // Empty state - neutral styling
      input.classList.remove('border-red-500', 'border-green-500', 'focus:ring-red-500', 'focus:ring-green-500', 'focus:border-red-500', 'focus:border-green-500')
      input.classList.add('border-gray-500', 'focus:ring-blue-500', 'focus:border-blue-500')
    } else if (isValid) {
      // Valid state - green styling
      input.classList.remove('border-gray-500', 'border-red-500', 'focus:ring-blue-500', 'focus:ring-red-500', 'focus:border-blue-500', 'focus:border-red-500')
      input.classList.add('border-green-500', 'focus:ring-green-500', 'focus:border-green-500')
    } else {
      // Invalid state - red styling
      input.classList.remove('border-gray-500', 'border-green-500', 'focus:ring-blue-500', 'focus:ring-green-500', 'focus:border-blue-500', 'focus:border-green-500')
      input.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500')
    }
  }

  updateErrorMessage(isValid, email) {
    const errorMessage = this.errorMessageTarget
    
    if (email && !isValid) {
      errorMessage.classList.remove('hidden')
    } else {
      errorMessage.classList.add('hidden')
    }
  }

  updateSubmitButton(isValid, email) {
    const button = this.submitButtonTarget
    
    if (email && isValid) {
      button.disabled = false
    } else {
      button.disabled = true
    }
  }
}