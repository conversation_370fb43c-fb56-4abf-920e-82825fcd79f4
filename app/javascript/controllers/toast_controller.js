import { Controller } from "@hotwired/stimulus";

// Toast 控制器，基于 Flowbite 组件样式
export default class extends Controller {
  static targets = [
    "successTemplate",
    "errorTemplate",
    "contentContainer",
  ];

  static values = {
    duration: { type: Number, default: 5 },
    autoClose: { type: Boolean, default: true },
    type: String,
    message: String,
  };

  connect() {


    const hasInitialMessage = this.messageValue && this.messageValue.trim() !== "";

    if (hasInitialMessage) {
      this.element.classList.remove("opacity-0", "pointer-events-none");
      this.element.classList.add("opacity-100");

      if (this.autoCloseValue) {
        this.startAutoCloseTimer();
      }
    } else {
      this.element.classList.add("opacity-0", "pointer-events-none");
      this.element.classList.remove("opacity-100");
    }

    // Listen for custom event to show toast dynamically via JS
    this.boundHandleToastShow = this.handleToastShow.bind(this);
    document.addEventListener("toast:show", this.boundHandleToastShow);
  }

  disconnect() {
    if (this.closeTimer) {
      clearTimeout(this.closeTimer);
    }
    document.removeEventListener("toast:show", this.boundHandleToastShow);
  }

  handleToastShow(event) {
    const { message, type, duration } = event.detail;
    this.showToast(message, type, duration);
  }

  startAutoCloseTimer() {
    if (this.closeTimer) {
      clearTimeout(this.closeTimer);
    }
    this.closeTimer = setTimeout(() => {
      this.close();
    }, this.durationValue * 1000);
  }

  showToast(messageContent, type = "success", duration = null) {
    this.messageValue = messageContent;
    this.typeValue = type;
    if (duration !== null) {
      this.durationValue = duration;
    }

    // Clear existing content
    this.contentContainerTarget.innerHTML = "";

    const templateTarget =
      this.typeValue === "success"
        ? this.successTemplateTarget
        : this.errorTemplateTarget;

    const clonedContent = templateTarget.content.cloneNode(true);

    const messageContainerInCloned = clonedContent.querySelector(
      '[data-toast-target="messageContainer"]'
    );

    if (messageContainerInCloned) {
      messageContainerInCloned.innerHTML = this.messageValue.replace(/\n/g, '<br>');
    }

    this.contentContainerTarget.appendChild(clonedContent);

    this.element.classList.remove("opacity-0", "pointer-events-none");
    this.element.classList.add("opacity-100");

    if (this.autoCloseValue) {
      this.startAutoCloseTimer();
    }
  }

  close() {
    this.element.classList.remove("opacity-100");
    this.element.classList.add("opacity-0");

    // After transition, then hide and clear content
    // Tailwind's default transition duration is 150ms for opacity.
    // We wait a bit longer to ensure transition completes.
    setTimeout(() => {
      if (this.element.classList.contains("opacity-0")) { // Check if it wasn't re-shown
        this.element.classList.add("pointer-events-none");
        this.contentContainerTarget.innerHTML = ""; // Clear content after fade
      }
    }, 300); // Should be >= opacity transition duration

    if (this.closeTimer) {
      clearTimeout(this.closeTimer);
      this.closeTimer = null;
    }
  }
}
