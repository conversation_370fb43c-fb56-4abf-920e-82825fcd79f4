import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [ "content", "toggleButton" ]
  static values = {
    expanded: { type: Boolean, default: false },
    showText: String,
    hideText: String
  }

  toggle() {
    this.expandedValue = !this.expandedValue
  }

  expandedValueChanged() {
    this.update()
  }

  update() {
    this.contentTarget.classList.toggle("hidden", !this.expandedValue)
    if (this.hasToggleButtonTarget) {
      this.toggleButtonTarget.textContent = this.expandedValue ? this.hideTextValue : this.showTextValue
    }
  }
}
