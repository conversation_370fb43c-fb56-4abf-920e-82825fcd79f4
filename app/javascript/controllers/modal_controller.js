import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["backdrop", "content"]

  connect() {
    // Show the modal when controller connects
    this.show()

    // Add escape key listener
    this.handleEscape = this.handleEscape.bind(this)
    document.addEventListener('keydown', this.handleEscape)

    // Add backdrop click listener
    this.handleBackdropClick = this.handleBackdropClick.bind(this)
    this.element.addEventListener('click', this.handleBackdropClick)
  }

  disconnect() {
    // Clean up event listeners
    document.removeEventListener('keydown', this.handleEscape)
    this.element.removeEventListener('click', this.handleBackdropClick)
  }

  show() {
    this.element.classList.remove('hidden')
    this.element.classList.add('flex')
    document.body.style.overflow = 'hidden'
  }

  hide() {
    this.element.classList.add('hidden')
    this.element.classList.remove('flex')
    document.body.style.overflow = ''

    // Clean up turbo frame if present
    const frame = this.element.closest("turbo-frame")
    if (frame) {
      frame.removeAttribute("src")
      this.element.remove()
    }
  }

  handleEscape(event) {
    if (event.key === 'Escape') {
      this.hide()
    }
  }

  handleBackdropClick(event) {
    // Only hide if clicking the backdrop itself, not content
    if (event.target === this.element) {
      this.hide()
    }
  }

  // Action method for close buttons
  close() {
    this.hide()
  }
}
