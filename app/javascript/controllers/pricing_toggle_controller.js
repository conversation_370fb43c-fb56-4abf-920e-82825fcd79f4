import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [ "monthlyButton", "yearlyButton", "monthlyPlans", "yearlyPlans" ]

  // Define classes for active and inactive states for the pill toggle
  // Active: Darker background (like the pill's thumb), white text.
  // Inactive: Transparent background (shows pill track), darker text.
  activeClasses = ['bg-slate-800', 'text-white'];
  inactiveClasses = ['bg-transparent', 'text-slate-300'];

  connect() {
    this.showMonthlyPlans();
  }

  showMonthlyPlans() {
    this.monthlyPlansTarget.classList.remove('hidden');
    this.yearlyPlansTarget.classList.add('hidden');
    this.styleButtonActive(this.monthlyButtonTarget, this.yearlyButtonTarget);
  }

  showYearlyPlans() {
    this.yearlyPlansTarget.classList.remove('hidden');
    this.monthlyPlansTarget.classList.add('hidden');
    this.styleButtonActive(this.yearlyButtonTarget, this.monthlyButtonTarget);
  }

  styleButtonActive(activeButton, inactiveButton) {
    activeButton.classList.remove(...this.activeClasses, ...this.inactiveClasses);
    inactiveButton.classList.remove(...this.activeClasses, ...this.inactiveClasses);

    activeButton.classList.add(...this.activeClasses);

    inactiveButton.classList.add(...this.inactiveClasses);
  }
}
