import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { text: String }

  copy() {
    const text = this.textValue

    navigator.clipboard.writeText(text).then(() => {
      // Create and dispatch a toast event to show flash message
      const event = new CustomEvent('toast:show', {
        detail: {
          type: 'success',
          message: this.element.dataset.clipboardMessageValue || `Email copied to clipboard: ${text}`,
          duration: 3
        }
      })
      document.dispatchEvent(event)
    }).catch(err => {
      console.error('Failed to copy text: ', err)
      // Fallback for older browsers
      this.fallbackCopyTextToClipboard(text)
    })
  }

  fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea")
    textArea.value = text
    textArea.style.top = "0"
    textArea.style.left = "0"
    textArea.style.position = "fixed"

    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    try {
      const successful = document.execCommand('copy')
      if (successful) {
        const event = new CustomEvent('toast:show', {
          detail: {
            type: 'success',
            message: this.element.dataset.clipboardMessageValue || `Email copied to clipboard: ${text}`,
            duration: 3
          }
        })
        document.dispatchEvent(event)
      }
    } catch (err) {
      console.error('Fallback: Could not copy text: ', err)
    }

    document.body.removeChild(textArea)
  }
}
