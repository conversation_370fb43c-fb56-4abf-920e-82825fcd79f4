import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["card", "closeButton"]
  static values = { pricingUrl: String }

  close() {
    this.cardTarget.style.display = 'none'
  }

  visitPlans(event) {
    if (this.closeButtonTarget.contains(event.target)) {
      return
    }

    const url = this.hasPricingUrlValue ? this.pricingUrlValue : '/pricing'
    window.top.location.href = url
  }
}
