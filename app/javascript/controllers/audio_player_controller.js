import { Controller } from "@hotwired/stimulus"

/**
 * Modern Audio Player Controller
 * Handles both global and individual audio players with universal compatibility
 * Enhanced Safari compatibility with clean, maintainable code
 */
export default class extends Controller {
  static targets = [
    "audio",
    "playButton", "playIcon", "pauseIcon",
    "progressBar", "progressContainer",
    "currentTime", "duration",
    "volumeButton", "volumeSlider", "volumeFill",
    "albumImage", "defaultIcon",
    "songTitle", "songArtist",
    "errorMessage"
  ]

  static values = {
    mode: { type: String, default: "global" },
    autoplay: { type: <PERSON>olean, default: false },
    debug: { type: <PERSON>olean, default: false }
  }

  // Configuration constants for cleaner code
  static CONFIG = {
    RETRY_ATTEMPTS: 3,
    WAIT_TIMES: {
      DEFAULT: 50,
      SAFARI: 200,
      SAFARI_REINIT: 200,
      RETRY_BASE: 100,
      SAFARI_RETRY_BASE: 300
    },
    TIMEOUTS: {
      SAFARI_LOAD: 5000,
      SEEK_RETRY: 100
    },
    ERROR_MESSAGES: {
      1: 'Permission denied',
      2: 'Network error',
      3: 'Decoding error',
      4: 'Source not supported'
    }
  }

  connect() {
    this.setupController()
    this.setupAudioElement()
    this.setupEventListeners()
    this.setupAudioCompatibility()
    this.setupSafariCompatibility()

    if (this.debugValue) {
      this.log('Audio player connected', { mode: this.modeValue })
    }

    if (this.isGlobalPlayer) {
      this.hidePlayer();
    }
  }

  disconnect() {
    this.cleanup()
  }

  // === SETUP METHODS ===

  setupController() {
    this.initializeState()
    this.setupBrowserDetection()
    this.bindMethods()
    this.setupGlobalListeners()
  }

  initializeState() {
    this.state = {
      hasUserInteracted: false,
      isPlaying: false,
      volume: 1.0,
      currentSrc: null,
      retryCount: 0,
      isUserInitiatedPlay: false,
      safariReady: false,
      audioContextResumed: false
    }
  }

  setupBrowserDetection() {
    this.isGlobalPlayer = this.modeValue === "global"
    this.isSafariBrowser = this.detectSafari()
  }

  bindMethods() {
    this.boundHandleLoadSong = this.handleLoadSong.bind(this)
    this.boundHandleUserInteraction = this.handleUserInteraction.bind(this)
  }

  setupGlobalListeners() {
    if (this.isGlobalPlayer) {
      document.addEventListener('loadSong', this.boundHandleLoadSong)
    }

    // Safari: Listen for any user interaction to enable audio
    const interactionEvents = ['click', 'touchstart']
    interactionEvents.forEach(event => {
      document.addEventListener(event, this.boundHandleUserInteraction, { once: false })
    })
  }

  detectSafari() {
    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent) ||
           /iPad|iPhone|iPod/.test(navigator.userAgent)
  }

  setupAudioElement() {
    if (!this.hasAudioTarget) return

    const audio = this.audioTarget

    // Universal audio attributes (work safely on all browsers)
    audio.setAttribute('playsinline', '')
    audio.setAttribute('webkit-playsinline', '')

    // Set initial volume
    audio.volume = this.state.volume
    audio.preload = 'metadata'

    this.updateUI()
  }

  setupEventListeners() {
    if (!this.hasAudioTarget) return

    const audio = this.audioTarget
    const events = {
      // Playback events
      'play': () => this.handlePlay(),
      'pause': () => this.handlePause(),
      'ended': () => this.handleEnded(),
      'timeupdate': () => this.handleTimeUpdate(),
      'loadedmetadata': () => this.handleLoadedMetadata(),

      // Loading events
      'loadstart': () => this.handleLoadStart(),
      'canplay': () => this.handleCanPlay(),
      'waiting': () => this.handleWaiting(),

      // Error events
      'error': (e) => this.handleError(e),
      'stalled': () => this.handleStalled(),

      // Volume events
      'volumechange': () => this.handleVolumeChange()
    }

    // Add event listeners
    Object.entries(events).forEach(([event, handler]) => {
      audio.addEventListener(event, handler)
    })

    // Store for cleanup
    this.eventListeners = Object.entries(events)
  }

  setupAudioCompatibility() {
    // Audio context unlock for all browsers (safe universal approach)
    this.unlockAudioContext()
  }

    setupSafariCompatibility() {
    this.initializeAudioContext()
    this.setupVisibilityHandling()

    if (this.isSafariBrowser) {
      this.prepareSafariAudio()
    }
  }

  setupVisibilityHandling() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.state.isPlaying) {
        this.handleVisibilityChange()
      }
    })
  }

  prepareSafariAudio() {
    if (!this.hasAudioTarget) return

    try {
      const audio = this.audioTarget

      // Safari-specific attributes
      audio.setAttribute('x-webkit-airplay', 'allow')
      audio.setAttribute('controls', false)

      audio.load()
      this.state.safariReady = true
      this.log('Safari audio initialization complete')
    } catch (error) {
      this.log('Safari audio initialization failed:', error)
    }
  }

    initializeAudioContext() {
    const AudioContextClass = window.AudioContext || window.webkitAudioContext
    if (!AudioContextClass) return

    try {
      this.audioContext = new AudioContextClass()

      if (this.audioContext.state === 'suspended') {
        this.log('AudioContext suspended, will resume on user interaction')
      }
    } catch (error) {
      this.log('AudioContext not available:', error)
    }
  }

  initializeSafariAudio() {
    // Safari: Prepare audio element for better compatibility
    if (this.hasAudioTarget) {
      try {
        const audio = this.audioTarget

        // Safari-specific attributes
        audio.setAttribute('x-webkit-airplay', 'allow')
        audio.setAttribute('controls', false)

        // Safari: Load empty audio to initialize
        audio.load()

        // Safari: Mark as ready for Safari-specific handling
        this.state.safariReady = true

        this.log('Safari audio initialization complete')
      } catch (error) {
        this.log('Safari audio initialization failed:', error)
      }
    }
  }

  handleUserInteraction() {
    if (!this.state.hasUserInteracted) {
      this.state.hasUserInteracted = true
      this.resumeAudioContext()
      this.log('User interaction detected - audio enabled')
    }
  }

  async resumeAudioContext() {
    // Safari: Resume AudioContext on user interaction
    if (this.audioContext && this.audioContext.state === 'suspended' && !this.state.audioContextResumed) {
      try {
        await this.audioContext.resume()
        this.state.audioContextResumed = true
        this.log('AudioContext resumed')
      } catch (error) {
        this.log('Failed to resume AudioContext:', error)
      }
    }
  }

  handleVisibilityChange() {
    // Safari: Handle page visibility changes
    if (document.hidden && this.audioContext) {
      this.log('Page hidden - audio may be affected')
    } else if (!document.hidden && this.audioContext && this.audioContext.state === 'suspended') {
      this.resumeAudioContext()
    }
  }



  // === AUDIO COMPATIBILITY ===

  async unlockAudioContext() {
    if (this.state.hasUserInteracted) return

    const unlock = async () => {
      try {
        // Create silent audio to unlock context (universal approach)
        const silentAudio = new Audio()
        silentAudio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA'

        await silentAudio.play()
        silentAudio.pause()
        silentAudio.remove()

        this.state.hasUserInteracted = true
        this.log('Audio context unlocked')

        // Remove listeners
        document.removeEventListener('click', unlock)
        document.removeEventListener('touchend', unlock)
        document.removeEventListener('keydown', unlock)

      } catch (error) {
        this.log('Failed to unlock audio context:', error)
      }
    }

    // Listen for user interaction
    document.addEventListener('click', unlock, { once: true, passive: true })
    document.addEventListener('touchend', unlock, { once: true, passive: true })
    document.addEventListener('keydown', unlock, { once: true, passive: true })
  }

  // === PUBLIC API ===

  async loadSong(audioUrl, metadata = {}) {
    if (!audioUrl) {
      this.clearSong()
      return
    }

    try {
      this.showLoading(true)
      this.hideError()

      if (this.isSafariBrowser && !this.state.hasUserInteracted) {
        this.showError('Tap to enable audio playback')
        return
      }

      await this.prepareAudioForNewSong(audioUrl, metadata)
      this.log('Song loaded:', { audioUrl, metadata })

    } catch (error) {
      this.handleLoadError(error)
    }
  }

  async prepareAudioForNewSong(audioUrl, metadata) {
    // Reset audio state
    this.audioTarget.pause()
    this.audioTarget.currentTime = 0

    // Update metadata and source
    this.updateMetadata(metadata)
    this.audioTarget.src = audioUrl
    this.state.currentSrc = audioUrl

    // Load audio based on browser
    if (this.isSafariBrowser) {
      await this.safariLoadAudio()
    } else {
      this.audioTarget.load()
    }
  }

    async safariLoadAudio() {
    return new Promise((resolve, reject) => {
      const audio = this.audioTarget
      const cleanup = () => {
        audio.removeEventListener('canplay', onSuccess)
        audio.removeEventListener('error', onError)
      }

      const onSuccess = () => {
        cleanup()
        resolve()
      }

      const onError = (error) => {
        cleanup()
        reject(error)
      }

      audio.addEventListener('canplay', onSuccess)
      audio.addEventListener('error', onError)
      audio.load()

      // Timeout fallback
      setTimeout(() => {
        cleanup()
        resolve() // Resolve anyway to prevent hanging
      }, this.constructor.CONFIG.TIMEOUTS.SAFARI_LOAD)
    })
  }

    async play() {
    if (!this.hasAudioTarget || !this.state.currentSrc) return

    try {
      this.state.hasUserInteracted = true
      await this.resumeAudioContext()
      await this.ensureAudioReady()
      await this.executePlay()

      this.state.retryCount = 0
    } catch (error) {
      await this.handlePlayError(error)
    }
  }

  async ensureAudioReady() {
    if (this.audioTarget.readyState >= 2) return

    if (this.isSafariBrowser) {
      if (!this.state.safariReady) {
        this.prepareSafariAudio()
      }
      this.audioTarget.load()
      await this.wait(this.constructor.CONFIG.WAIT_TIMES.SAFARI)
    } else {
      this.audioTarget.load()
      await this.wait(this.constructor.CONFIG.WAIT_TIMES.DEFAULT)
    }
  }

  async executePlay() {
    const playPromise = this.audioTarget.play()

    if (playPromise !== undefined) {
      await playPromise
    }
  }

  pause() {
    if (this.hasAudioTarget) {
      this.audioTarget.pause()
    }
  }

  async togglePlay() {
    this.state.hasUserInteracted = true

    if (this.state.isPlaying) {
      this.pause()
    } else {
      await this.play()
    }
  }

    seek(percentage) {
    if (!this.canSeek()) return

    const newTime = percentage * this.audioTarget.duration
    this.performSeek(newTime)
  }

  canSeek() {
    return this.hasAudioTarget &&
           this.audioTarget.duration &&
           isFinite(this.audioTarget.duration)
  }

  performSeek(newTime) {
    if (this.isSafariBrowser) {
      this.seekSafari(newTime)
    } else {
      this.audioTarget.currentTime = newTime
    }
  }

  seekSafari(newTime) {
    try {
      this.audioTarget.currentTime = newTime
    } catch (error) {
      this.log('Safari seek error:', error)
      // Retry after delay
      setTimeout(() => {
        try {
          this.audioTarget.currentTime = newTime
        } catch (retryError) {
          this.log('Safari seek retry failed:', retryError)
        }
      }, this.constructor.CONFIG.TIMEOUTS.SEEK_RETRY)
    }
  }

  setVolume(volume) {
    if (!this.hasAudioTarget) return

    this.state.volume = Math.max(0, Math.min(1, volume))
    this.audioTarget.volume = this.state.volume
  }

  toggleMute() {
    if (!this.hasAudioTarget) return

    this.audioTarget.muted = !this.audioTarget.muted
  }

  clearSong() {
    if (!this.hasAudioTarget) return

    this.audioTarget.pause()
    this.audioTarget.removeAttribute('src')
    this.state.currentSrc = null

    // Universal load call (safe for all browsers)
    this.audioTarget.load()

    this.updateMetadata({})
    this.updateUI()
    this.broadcastPlayStateToCards()
    if (this.isGlobalPlayer) {
      this.hidePlayer()
    }
  }

  // === EVENT HANDLERS ===

  handleLoadSong(event) {
    if (!this.isGlobalPlayer) return

    const { audioUrl, title, artist, imageUrl, userInitiated } = event.detail

    // If loading a different song, broadcast reset for previous song first
    if (this.state.currentSrc && this.state.currentSrc !== audioUrl) {
      document.dispatchEvent(new CustomEvent('playerStateChanged', {
        detail: {
          isPlaying: false,
          currentSrc: this.state.currentSrc,
          hasEnded: true // This will reset the previous song's cards
        }
      }))
    }

    if (userInitiated) {
      this.showPlayer()
      this.state.hasUserInteracted = true
      this.state.isUserInitiatedPlay = true
    }

    this.loadSong(audioUrl, { title, artist, imageUrl })
  }

  handlePlay() {
    this.state.isPlaying = true
    this.updatePlayButton()
    this.showLoading(false)
  }

  handlePause() {
    this.state.isPlaying = false
    this.updatePlayButton()

    // Remove streaming animation when paused
    if (this.hasProgressBarTarget) {
      this.progressBarTarget.classList.remove('streaming-progress')
    }
  }

  handleEnded() {
    this.state.isPlaying = false
    this.updatePlayButton()
    this.updateProgress(0)
    this.broadcastPlayStateToCards()
  }

  handleTimeUpdate() {
    if (this.hasValidDuration()) {
      const progress = this.audioTarget.currentTime / this.audioTarget.duration
      this.updateProgress(progress)
    } else if (this.state.isPlaying) {
      this.updateStreamingProgress()
    }
  }

  handleLoadedMetadata() {
    this.updateDuration()
    this.showLoading(false)
  }

  handleLoadStart() {
    this.showLoading(true)
  }

  handleCanPlay() {
    this.showLoading(false)
    if (this.state.isUserInitiatedPlay) {
      this.play()
      this.state.isUserInitiatedPlay = false
    }
  }

  handleWaiting() {
    this.showLoading(true)
  }

  handleVolumeChange() {
    this.updateVolumeUI()
  }

  handleStalled() {
    this.log('Audio stalled')
    this.showLoading(true)
  }

  handleError(event) {
    const error = this.audioTarget.error
    if (!error) return

    this.log('Audio error:', error)
    this.showLoading(false)

    const message = this.constructor.CONFIG.ERROR_MESSAGES[error.code] || 'Unknown audio error'
    this.showError(message)
  }

    async handlePlayError(error) {
    this.log('Play error:', error)

    const errorHandlers = {
      'NotAllowedError': () => this.showError('Tap to play (user interaction required)'),
      'NotSupportedError': () => this.showError('Audio format not supported')
    }

    if (errorHandlers[error.name]) {
      errorHandlers[error.name]()
      return
    }

    await this.retryPlay()
  }

  async retryPlay() {
    if (this.state.retryCount >= this.constructor.CONFIG.RETRY_ATTEMPTS) {
      this.showError('Playback failed')
      return
    }

    this.state.retryCount++
    this.log(`Retrying play (attempt ${this.state.retryCount})`)

    const delay = this.calculateRetryDelay()
    await this.wait(delay)

    if (this.shouldReinitializeSafari()) {
      this.prepareSafariAudio()
      await this.wait(this.constructor.CONFIG.WAIT_TIMES.SAFARI_REINIT)
    }

    await this.play()
  }

  calculateRetryDelay() {
    const baseDelay = this.isSafariBrowser ?
      this.constructor.CONFIG.WAIT_TIMES.SAFARI_RETRY_BASE :
      this.constructor.CONFIG.WAIT_TIMES.RETRY_BASE

    return baseDelay * this.state.retryCount
  }

  shouldReinitializeSafari() {
    return this.isSafariBrowser && this.state.retryCount === 2
  }

  handleLoadError(error) {
    this.log('Load error:', error)
    this.showError('Failed to load audio')
    this.showLoading(false)
  }

  // === UI UPDATES ===

  updatePlayButton() {
    if (!this.hasPlayButtonTarget) return

    const isPlaying = this.state.isPlaying

    if (this.hasPlayIconTarget && this.hasPauseIconTarget) {
      this.playIconTarget.classList.toggle('hidden', isPlaying)
      this.pauseIconTarget.classList.toggle('hidden', !isPlaying)
    }

    this.playButtonTarget.classList.toggle('playing', isPlaying)
  }

  updateProgress(percentage) {
    if (this.hasProgressBarTarget) {
      this.progressBarTarget.style.width = `${percentage * 100}%`
      this.progressBarTarget.classList.remove('streaming-progress')
    }

    if (this.hasCurrentTimeTarget && this.audioTarget) {
      this.currentTimeTarget.textContent = this.formatTime(this.audioTarget.currentTime)
    }
  }

  updateStreamingProgress() {
    if (this.hasProgressBarTarget) {
      this.progressBarTarget.classList.add('streaming-progress')
      this.progressBarTarget.style.width = '100%'
    }

    if (this.hasCurrentTimeTarget && this.audioTarget) {
      this.currentTimeTarget.textContent = this.formatTime(this.audioTarget.currentTime)
    }
  }

  updateDuration() {
    if (!this.hasDurationTarget || !this.audioTarget) return

    const duration = this.audioTarget.duration

    this.durationTarget.textContent = isFinite(duration) ?
      this.formatTime(duration) :
      'LIVE'
  }

  updateVolumeUI() {
    if (!this.hasAudioTarget) return

    const audio = this.audioTarget
    const isMuted = audio.muted || audio.volume === 0

    if (this.hasVolumeFillTarget) {
      this.volumeFillTarget.style.width = `${audio.volume * 100}%`
    }

    if (this.hasVolumeButtonTarget) {
      this.volumeButtonTarget.classList.toggle('muted', isMuted)
    }
  }

  updateMetadata(metadata = {}) {
    const { title = 'Select a song', artist = 'Choose a track to play', imageUrl } = metadata

    if (this.hasSongTitleTarget) {
      this.songTitleTarget.textContent = title
    }

    if (this.hasSongArtistTarget) {
      const displayArtist = artist.split(',')[0]?.trim() || artist
      this.songArtistTarget.textContent = displayArtist
    }

    this.updateAlbumImage(imageUrl)
  }

  updateAlbumImage(imageUrl) {
    if (!this.hasAlbumImageTarget || !this.hasDefaultIconTarget) return

    if (imageUrl) {
      this.albumImageTarget.src = imageUrl
      this.albumImageTarget.classList.remove('hidden')
      this.defaultIconTarget.classList.add('hidden')
    } else {
      this.albumImageTarget.classList.add('hidden')
      this.defaultIconTarget.classList.remove('hidden')
    }
  }

  updateUI() {
    this.updatePlayButton()
    this.updateProgress(0)
    this.updateDuration()
    this.updateVolumeUI()
  }

  showLoading(show) {
    this.element.classList.toggle('loading', show)
  }

  showError(message) {
    if (!this.hasErrorMessageTarget) return

    this.errorMessageTarget.textContent = message
    this.errorMessageTarget.classList.remove('hidden')

    if (this.isGlobalPlayer) {
      this.showPlayer()
    }
  }

  hideError() {
    if (this.hasErrorMessageTarget) {
      this.errorMessageTarget.classList.add('hidden')
    }
  }

  // === ACTION HANDLERS ===

  playPause() {
    this.togglePlay()
  }

  seekTo(event) {
    if (!this.hasProgressContainerTarget) return

    const rect = this.progressContainerTarget.getBoundingClientRect()
    const x = event.clientX - rect.left
    const percentage = Math.max(0, Math.min(1, x / rect.width))

    this.seek(percentage)
  }

  skipBackward() {
    if (this.hasAudioTarget) {
      this.audioTarget.currentTime = Math.max(0, this.audioTarget.currentTime - 10)
    }
  }

  skipForward() {
    if (!this.hasAudioTarget) return

    if (this.hasValidDuration()) {
      this.audioTarget.currentTime = Math.min(
        this.audioTarget.duration,
        this.audioTarget.currentTime + 10
      )
    } else {
      // For streaming audio
      this.audioTarget.currentTime = this.audioTarget.currentTime + 10
    }
  }

  hasValidDuration() {
    return this.audioTarget?.duration && isFinite(this.audioTarget.duration)
  }

  adjustVolume(event) {
    if (!this.hasVolumeSliderTarget) return

    const rect = this.volumeSliderTarget.getBoundingClientRect()
    const x = event.clientX - rect.left
    const percentage = Math.max(0, Math.min(1, x / rect.width))

    this.setVolume(percentage)
  }

  mute() {
    this.toggleMute()
  }

  // === VISIBILITY CONTROL ===
  showPlayer() {
    this.element.classList.remove('hidden')
  }

  hidePlayer() {
    this.element.classList.add('hidden')
  }

  // === CARD SYNCHRONIZATION ===

  broadcastPlayStateToCards() {
    // Broadcast state change to all song cards and detail cards
    document.dispatchEvent(new CustomEvent('playerStateChanged', {
      detail: {
        isPlaying: this.state.isPlaying,
        currentSrc: this.state.currentSrc,
        hasEnded: !this.state.currentSrc // true when song is cleared/ended
      }
    }))
  }

  // === UTILITY METHODS ===

  formatTime(seconds) {
    if (!seconds || isNaN(seconds) || !isFinite(seconds)) return '0:00'

    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  log(...args) {
    if (this.debugValue || window.location.search.includes('debug=audio')) {
      console.log('[AudioPlayer]', ...args)
    }
  }

  cleanup() {
    this.removeAudioEventListeners()
    this.removeGlobalListeners()
    this.removeInteractionListeners()
    this.closeAudioContext()
  }

  removeAudioEventListeners() {
    if (this.eventListeners && this.hasAudioTarget) {
      this.eventListeners.forEach(([event, handler]) => {
        this.audioTarget.removeEventListener(event, handler)
      })
    }
  }

  removeGlobalListeners() {
    if (this.isGlobalPlayer && this.boundHandleLoadSong) {
      document.removeEventListener('loadSong', this.boundHandleLoadSong)
    }
  }

  removeInteractionListeners() {
    if (this.boundHandleUserInteraction) {
      ['click', 'touchstart'].forEach(event => {
        document.removeEventListener(event, this.boundHandleUserInteraction)
      })
    }
  }

  closeAudioContext() {
    if (this.audioContext?.state !== 'closed') {
      try {
        this.audioContext.close()
      } catch (error) {
        this.log('Error closing AudioContext:', error)
      }
    }
  }
}
