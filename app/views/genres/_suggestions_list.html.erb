<%# This partial is rendered by GenreController#index and replaces the content of the turbo-frame %>

<div class="absolute z-10 mt-1 w-full bg-slate-700 border border-slate-600 rounded-md shadow-lg max-h-60 overflow-auto
            <%= "hidden" if @suggestions.blank? && @query_from_user.blank? %>">
  <% if @suggestions.any? %>
    <% @suggestions.each do |suggestion| %>
      <div class="suggestion-item px-3 py-1.5 text-sm text-slate-200 hover:bg-slate-600 cursor-pointer"
           data-tag-value="<%= suggestion.name %>"
           data-action="mousedown->searchable-tag-input#addTag">
        <%= suggestion.name %>
      </div>
    <% end %>
  <% else %>
    <% if @query_from_user.present? && @query_from_user.length >= 1 %>
      <div class="px-3 py-1.5 text-sm text-slate-400">No matching tags found.</div>
      <div class="suggestion-item px-3 py-1.5 text-sm text-slate-200 hover:bg-slate-600 cursor-pointer"
           data-tag-value="<%= @query_from_user %>"
           data-action="mousedown->searchable-tag-input#addTag"
           data-is-new-value="true">
        Add "<%= @query_from_user %>"
      </div>
    <% end %>
  <% end %>
</div>
