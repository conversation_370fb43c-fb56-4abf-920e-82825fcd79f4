<%# app/views/songs/index.html.erb %>
<% if @query_from_user.present? %>
  <%# Search results only - form stays in place %>
  <turbo-frame id="song_cards_list">
    <%= render "songs/song_cards_list", songs: @songs %>
  </turbo-frame>
<% else %>
  <%# Initial library view, now wrapped in its own frame %>
  <turbo-frame id="song_library_frame">
    <%= render "songs/song_library", songs: @songs, tasks_in_progress: @tasks_in_progress %>
  </turbo-frame>
<% end %>
