<%# app/views/songs/_song_library.html.erb %>
<div class="w-full bg-slate-800 border border-slate-600 rounded-lg shadow-lg overflow-hidden flex flex-col h-full">
  <div class="flex-1 overflow-y-auto p-6 lg:p-4 main-scrollable">
    <%# Search Bar and Filter Buttons - Responsive layout %>
    <div class="mb-6 lg:mb-4 flex flex-col sm:flex-row items-stretch sm:items-center gap-4 lg:gap-3 flex-shrink-0">
    <div class="flex-1 relative" data-controller="song-search">
      <%= render "songs/search_form", query_value: "" %>
    </div>

    <%# Filter Buttons: All / Liked - Better mobile spacing %>
    <div class="relative flex bg-slate-700 rounded-full p-1 shadow-inner w-full sm:w-auto" role="group">
      <% if current_user %>
        <%= link_to t('songs.index.all'),
            songs_path(filter: "all"),
            data: { turbo_frame: "song_library_frame" },
            class: "cursor-pointer flex-1 sm:flex-initial px-6 sm:px-4 py-3 sm:py-2 rounded-full text-base sm:text-sm font-medium transition-colors duration-200 ease-in-out #{params[:filter] != 'liked' ? 'text-white bg-blue-600 hover:bg-blue-500' : 'text-slate-300 hover:text-white hover:bg-slate-600'}" %>

        <%= link_to t('songs.index.liked'),
            songs_path(filter: "liked"),
            data: { turbo_frame: "song_library_frame" },
            class: "cursor-pointer flex-1 sm:flex-initial px-6 sm:px-4 py-3 sm:py-2 rounded-full text-base sm:text-sm font-medium transition-colors duration-200 ease-in-out #{params[:filter] == 'liked' ? 'text-white bg-blue-600 hover:bg-blue-500' : 'text-slate-300 hover:text-white hover:bg-slate-600'}" %>
      <% else %>
        <%# For non-logged in users, use root_path with filter params %>
        <%= link_to t('songs.index.all'),
            root_path(filter: "all"),
            data: { turbo_frame: "song_library_frame" },
            class: "cursor-pointer flex-1 sm:flex-initial px-6 sm:px-4 py-3 sm:py-2 rounded-full text-base sm:text-sm font-medium transition-colors duration-200 ease-in-out #{params[:filter] != 'liked' ? 'text-white bg-blue-600 hover:bg-blue-500' : 'text-slate-300 hover:text-white hover:bg-slate-600'}" %>

        <%= link_to t('songs.index.liked'),
            new_session_path,
            data: { turbo_frame: "modal_frame" },
            class: "cursor-pointer flex-1 sm:flex-initial px-6 sm:px-4 py-3 sm:py-2 rounded-full text-base sm:text-sm font-medium transition-colors duration-200 ease-in-out text-slate-300 hover:text-white hover:bg-slate-600 text-center block" %>
      <% end %>
    </div>
  </div>

    <% if (!current_user || current_user.free?) && !songs.empty? %>
      <%= render 'shared/subscription_card' %>
    <% end %>

    <%# Song Cards Area %>
    <% if current_user %>
      <%= turbo_stream_from [current_user, 'songs'] %>
    <% end %>

    <div class="space-y-4 lg:space-y-3 mb-24" data-fake-song-target="songList">
      <turbo-frame class="space-y-4 lg:space-y-3" id="song_cards_list">
        <%= render 'songs/song_cards_list',
                   songs: songs,
                   tasks: (params[:filter] == 'liked' ? [] : tasks_in_progress) %>
      </turbo-frame>
    </div>

  </div>
</div>
