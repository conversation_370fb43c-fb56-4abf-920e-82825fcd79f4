<%# app/views/songs/_song_cards_list.html.erb %>
<% tasks ||= [] %>

<%# ---- 1. unfinished tasks ---- %>
<% tasks.each do |task| %>
  <% 2.times do |index| %>
    <turbo-frame id="generation_task_<%= task.id %>_<%= index %>" class="block">
      <%= render 'shared/generation_task_card',
                 task: task,
                 index: index %>
    </turbo-frame>
  <% end %>
<% end %>

<%# ---- 2. regular songs (what was already there) ---- %>
<% if songs.any? || current_user.nil? %>
  <% songs.each do |song| %>
    <turbo-frame id="<%= dom_id(song) %>" class="block">
      <%= render 'shared/song_card', song: song %>
    </turbo-frame>
  <% end %>
<% else %>
  <p class="text-slate-400 text-center py-10"><%= t('shared.song_cards_list.no_songs_found') %></p>
<% end %>
