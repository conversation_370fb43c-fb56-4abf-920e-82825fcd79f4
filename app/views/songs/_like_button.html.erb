<% variant     = local_assigns.fetch(:variant, :list)          # :list or :detail
   with_label  = (variant == :detail)                          # only detail shows text
   size_class  = (variant == :list) ? "w-6 h-6" : "w-4 h-4"
   wrapper_css = (variant == :list) ? "cursor-pointer p-2 rounded-full hover:bg-slate-600 focus:outline-none"
                                    : "cursor-pointer flex items-center gap-2 px-4 py-2 border border-slate-600 text-slate-300 rounded-md" %>

<%= button_to toggle_like_song_path(song),
              method: :patch,
              data: { "turbo-stream": true },
              form_class: "contents",
              onclick: "event.stopPropagation();",
              class: wrapper_css do %>

  <svg class="<%= size_class %> transition-colors duration-150
              <%= song.is_liked? ? 'text-red-500 fill-current'
                                 : 'text-slate-400 hover:text-red-400' %>"
       viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
  </svg>

  <% if with_label %>
    <%= song.is_liked? ? t('songs.show.liked') : t('songs.show.like') %>
  <% end %>
<% end %>
