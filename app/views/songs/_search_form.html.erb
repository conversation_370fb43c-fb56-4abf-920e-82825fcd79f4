<%# app/views/songs/_search_form.html.erb %>
<%= form_with url: songs_path, method: :get,
              data: {
                turbo_frame: "song_cards_list",
                song_search_target: "form"
              } do |form| %>
  <div class="relative">
    <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
      <%= image_tag "/search_icon.svg", class: "h-4 w-4 text-gray-400" %>
    </div>
    <div class="flex items-center w-full p-2 ps-10 text-sm border rounded-lg bg-slate-700 border-slate-600 min-h-[42px]">
      <%= form.text_field :query,
                          value: query_value,
                          placeholder: t('songs.index.search_placeholder'),
                          data: {
                            song_search_target: "input",
                            action: "input->song-search#search"
                          },
                          autocomplete: "off",
                          class: "flex-grow bg-transparent text-slate-300 placeholder-slate-400 placeholder:text-sm text-sm border-0 focus:ring-0 focus:outline-none p-0 min-w-[150px]" %>
    </div>
  </div>
<% end %>
