<%# app/views/songs/_song_detail.html.erb %>
<%#
  Song detail panel (right column)
  This partial is rendered in two scenarios:
  1. Initial page load with song = nil (shows skeleton)
  2. When user clicks a song card (replaced via Turbo Frame with actual song data)
%>
<% if local_assigns[:show_create_first] %>
  <%# --- Create First Song Card --- %>
  <div class="w-full h-full bg-slate-800 border border-slate-600 rounded-lg shadow-lg flex flex-col relative overflow-hidden"
       data-controller="song-detail"
       data-song-detail-song-id-value="">

    <%# Background gradient %>
    <div class="absolute inset-0 bg-gradient-to-br from-slate-800 via-purple-900/20 to-blue-900/20 rounded-lg"></div>

    <div data-song-detail-target="createFirst" class="h-full flex flex-col items-center justify-center text-center relative z-10">
      <div class="relative max-w-xs px-6">
        <%# Music icon %>
        <div class="mb-8 relative">
          <div class="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 rounded-full flex items-center justify-center shadow-lg">
            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3">
              </path>
            </svg>
          </div>
        </div>

        <%# Main heading %>
        <div class="mb-6">
          <h2 class="text-3xl font-bold mb-2 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
            <%= t('songs.show.create_first_song') %>
          </h2>
        </div>

        <%# Subtitle %>
        <p class="text-slate-300 text-sm mb-8 leading-relaxed">
          <%= t('songs.show.transform_ideas') %>
        </p>


        <%# Feature indicators %>
        <div class="flex items-center justify-center gap-6 mt-8 text-xs">
          <div class="flex items-center gap-2 px-3 py-2 bg-slate-700/60 rounded-full">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span class="text-green-300"><%= t('songs.show.ai_powered') %></span>
          </div>
          <div class="flex items-center gap-2 px-3 py-2 bg-slate-700/60 rounded-full">
            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span class="text-blue-300"><%= t('songs.show.instant_results') %></span>
          </div>
        </div>
      </div>
    </div>
  </div>
<% else %>
  <%# --- Regular Song Detail Card --- %>
  <div class="w-full h-full bg-slate-800 border border-slate-600 rounded-lg shadow-lg overflow-hidden flex flex-col"
       data-controller="song-detail"
       data-song-detail-song-id-value="<%= song&.id || '' %>">

    <div class="flex-1 overflow-y-auto p-6 main-scrollable">
      <%# --- Image Section --- %>
      <div class="flex-shrink-0 mb-4">
      <div class="w-full mx-auto aspect-square rounded-md overflow-hidden shadow-md">
        <% if song&.image_url.present? %>
          <%= image_tag song.image_url,
              class: "w-full h-full object-cover",
              data: { song_detail_target: "songImage" } %>
        <% else %>
          <div class="w-full h-full bg-slate-700 flex items-center justify-center"
               data-song-detail-target="imagePlaceholder">
            <svg class="w-16 h-16 text-slate-500"
                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
            </svg>
          </div>
        <% end %>
      </div>
    </div>

    <%# --- Text Content Section --- %>
    <% if song %>
      <div class="flex-1 min-h-0">
        <div data-song-detail-target="content" class="opacity-100 transition-opacity duration-300">
          <%# Title %>
          <h2 class="text-2xl font-bold text-white mb-2 truncate h-8 leading-8"
              title="<%= song.title.presence || t('songs.show.untitled_song') %>"
              data-song-detail-target="songTitle">
            <%= song.title.presence || t('songs.show.untitled_song') %>
          </h2>

          <%# Tags %>
          <div class="mb-3 h-6 flex items-center flex-wrap gap-1">
            <% if song.style.present? %>
              <span class="text-xs font-semibold inline-flex py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200 truncate-tags">
                <%= song.style %>
              </span>
            <% else %>
              &nbsp;
            <% end %>
          </div>

          <%# Action Buttons %>
          <div class="mb-4 flex flex-col sm:flex-row gap-2 sm:gap-3">
            <% if song.audio_url.present? %>
              <button class="cursor-pointer w-full sm:w-auto flex items-center justify-center gap-2 px-4 py-2.5 sm:py-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-md transition-all duration-200 shadow-lg text-sm sm:text-base"
                      data-audio-url="<%= song.audio_url %>"
                      data-song-title="<%= song.title.presence || t('songs.show.untitled_song') %>"
                      data-song-image-url="<%= song.image_url.presence || asset_path('default_album_art_small.png') %>"
                      data-action="click->song-detail#playPause"
                      data-song-detail-target="playButton">
                <%# Play icon %>
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" data-song-detail-target="playIcon">
                  <path d="M8 5v14l11-7z"/>
                </svg>
                <%# Pause icon %>
                <svg class="w-4 h-4 hidden" fill="currentColor" viewBox="0 0 24 24" data-song-detail-target="pauseIcon">
                  <path d="M6 4h4v16H6zm8 0h4v16h-4z"/>
                </svg>
                <span data-song-detail-target="playText"
                      data-play-text-value="<%= t('songs.show.play') %>"
                      data-pause-text-value="<%= t('songs.show.pause') %>"><%= t('songs.show.play') %></span>
              </button>
            <% end %>

            <div class="w-full sm:w-auto">
              <turbo-frame id="<%= dom_id(song, :detail_like) %>">
                <%= render 'songs/like_button', song: song, variant: :detail %>
              </turbo-frame>
            </div>
          </div>

          <%# Lyrics %>
          <div class="text-sm text-slate-300 whitespace-pre-wrap min-h-[100px] mb-24">
            <% if song.lyrics.present? %>
              <%= simple_format(song.lyrics) %>
            <% else %>
              <p class="text-slate-400 italic"><%= t('songs.show.no_lyrics') %></p>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
    </div>
  </div>
<% end %>
