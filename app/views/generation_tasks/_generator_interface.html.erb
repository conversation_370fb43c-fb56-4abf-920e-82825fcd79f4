<div class="flex flex-col w-full h-full"
     data-controller="tabs openai-stream"
     data-openai-stream-surprise-me-url-value="<%= generate_surprise_me_stream_generation_tasks_path %>"
     data-openai-stream-lyrics-url-value="<%= generate_lyrics_stream_generation_tasks_path %>"
>
  <%= turbo_stream_from "openai_stream_#{session.id&.to_s || request.remote_ip}" %>
  <%= form_with(url: generation_tasks_path, method: :post,
                html: {
                  class: "w-full h-full bg-slate-800 border border-slate-600 rounded-lg shadow-lg overflow-hidden flex flex-col",
                  data: {
                    controller: "form-reset lyrics-validation guest-validation form-persistence",
                    form_persistence_storage_key_value: "generator-form",
                    form_reset_target: "form",
                    action: "submit->lyrics-validation#validateForm",
                    guest_validation_is_logged_in_value: current_user.present?
                  }
                }) do |form| %>
    <ul class="flex flex-wrap text-sm font-medium text-center text-slate-400 border-b border-slate-700 flex-shrink-0">
      <li>
        <button type="button"
                data-tabs-target="descriptionTab"
                data-action="click->tabs#changeTab"
                data-tab-name="description"
                aria-current="page"
                class="inline-block p-4 lg:p-3 text-blue-500 bg-slate-700 rounded-t-lg active cursor-pointer text-sm lg:text-xs"><%= t('.description_mode') %></button>
      </li>
      <li>
        <button type="button"
                data-tabs-target="lyricsTab"
                data-action="click->tabs#changeTab"
                data-tab-name="lyrics"
                class="inline-block p-4 lg:p-3 rounded-t-lg hover:text-slate-300 hover:bg-slate-700 cursor-pointer text-sm lg:text-xs"><%= t('.lyrics_mode') %></button>
      </li>
      <li class="ms-auto me-2"> <%# ms-auto to push to the right %>
        <button type="button"
                data-action="click->form-reset#clearForm"
                class="inline-block p-4 lg:p-3 text-slate-400 hover:text-slate-200 text-[10px] cursor-pointer"><%= t('.clear_all') %></button>
      </li>
    </ul>

    <div class="flex-1 overflow-y-auto p-6 lg:p-4 main-scrollable"> <%# Scrollable content area %>
      <%# Description Mode Content - Target for Stimulus controller %>
      <div id="description-mode-content" data-tabs-target="descriptionContent" class="space-y-6 lg:space-y-4">
        <%= render 'shared/textarea_input',
                   label_text: t('.description'),
                   input_id: 'song_description',
                   input_name: 'generation_task[description]',
                   rows_count: 5,
                   placeholder_text: t('.description_placeholder'),
                   button_text: t('.surprise_me'),
                   button_identifier: 'surprise_me',
                   character_limit: 350,
                   textarea_html_attributes: {
                     "data-fake-song-target": "descriptionInput"
                   }
        %>

        <p class="my-4"></p>

        <%= render 'shared/searchable_tag_input',
                   label_text: t('.genre'),
                   section_id: 'description_genre',
                   input_name: 'generation_task[genres][]',
                   placeholder_text: t('.genre_placeholder'),
                   quick_add_tags: t('generation_tasks.quick_add_tags.genres'),
                   category: 'genre' %>

        <%= render 'shared/searchable_tag_input',
                   label_text: t('.moods'),
                   section_id: 'description_moods',
                   input_name: 'generation_task[moods][]',
                   placeholder_text: t('.moods_placeholder'),
                   quick_add_tags: t('generation_tasks.quick_add_tags.moods'),
                   category: 'mood' %>

        <%= render 'shared/selectable_tag_group',
                   label_text: t('.vocal'),
                   input_name_prefix: 'generation_task[vocals]',
                   html_id_context: 'desc_vocals',
                   tags: [
                     { value: 'male_voice', display_text: t('.male_voice') },
                     { value: 'female_voice', display_text: t('.female_voice'), is_selected_by_default: true },
                     { value: 'instrumental', display_text: t('.instrumental') }
                   ] %>

        <%# Collapsible section for advanced options in Description Mode %>
        <%= render 'shared/collapsible_section', show_text: t('.show_more_options'), hide_text: t('.hide_advanced_options') do %>
          <%= render 'shared/selectable_tag_group',
                     label_text: t('generation_tasks.generator_interface.tempo'),
                     input_name_prefix: 'generation_task[tempo]',
                     html_id_context: 'desc_tempo',
                     tags: [
                       { value: '60-80', display_text: t('generation_tasks.generator_interface.tempo_slow'), is_selected_by_default: true },
                       { value: '80-120', display_text: t('generation_tasks.generator_interface.tempo_medium') },
                       { value: '120-160', display_text: t('generation_tasks.generator_interface.tempo_fast') }
                     ] %>

          <%= render 'shared/searchable_tag_input',
                     label_text: t('generation_tasks.generator_interface.instrument'),
                     section_id: 'description_instrument',
                     input_name: 'generation_task[instruments][]',
                     placeholder_text: t('generation_tasks.generator_interface.instrument_placeholder'),
                     quick_add_tags: t('generation_tasks.quick_add_tags.instruments'),
                     category: 'instrument' %>

          <%# TODO: Add duration back in %>
          <%#= render 'shared/selectable_tag_group',
                     label_text: t('generation_tasks.generator_interface.duration'),
                     input_name_prefix: 'generation_task[duration]',
                     html_id_context: 'desc_duration',
                     tags: [
                       { value: '2min', display_text: t('generation_tasks.generator_interface.duration_2_min'), is_selected_by_default: true },
                       { value: '4min', display_text: t('generation_tasks.generator_interface.duration_4_min'), is_premium: true }
                     ] %>
        <% end %>
      </div>

      <%# Lyrics Mode Content - includes toggles for Instrumental & Custom Style %>
      <div id="lyrics-mode-content" data-tabs-target="lyricsContent" data-controller="lyrics-options" class="hidden space-y-6 lg:space-y-4">

        <%# Instrumental Toggle %>
        <div class="flex items-center justify-between">
          <span class="text-base lg:text-sm font-medium text-slate-300"><%= t('.instrumental') %></span>
          <label class="inline-flex items-center cursor-pointer">
            <input type="checkbox" name="generation_task[instrumental]" value="1"
                   data-lyrics-options-target="instrumentalCheckbox"
                   data-action="change->lyrics-options#instrumentalToggled"
                   class="sr-only peer">
            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 dark:peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <%# Lyrics Textarea %>
        <%= render 'shared/textarea_input',
                   label_text: t('.lyrics'),
                   input_id: 'song_lyrics',
                   input_name: 'generation_task[lyrics]',
                   rows_count: 5,
                   placeholder_text: t('.lyrics_placeholder'),
                   button_text: t('.generate_lyrics'),
                   button_identifier: 'generate_lyrics',
                   character_limit: 3000,
                   textarea_html_attributes: {
                    data: {
                      "lyrics-options-target": "lyricsTextarea"
                    }
                   } %>

        <div>
          <label for="song_title" class="block mb-2 text-base lg:text-sm font-medium text-slate-300"><%= t('.title') %></label>
          <input type="text" id="song_title" name="generation_task[title]" placeholder="<%= t('.title_placeholder') %>"
                 data-lyrics-validation-target="titleInput"
                 class="block p-3 lg:p-2.5 w-full text-base lg:text-sm text-slate-200 bg-slate-700 rounded-lg border border-slate-600 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400" />
          <div data-lyrics-validation-target="titleError" class="hidden mt-1 text-sm text-red-400">
            <%= t('.title_error') %>
          </div>
        </div>

        <%# Custom Style Toggle %>
        <div class="flex items-center justify-center gap-4">
          <span class="text-base lg:text-sm font-medium text-slate-300"><%= t('.custom_style') %></span>
          <label class="inline-flex items-center cursor-pointer">
            <input type="checkbox" name="generation_task[use_custom_style]" value="1"
                   data-lyrics-options-target="customStyleCheckbox"
                   data-action="change->lyrics-options#customStyleToggled"
                   class="sr-only peer">
            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 dark:peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <%# Style Textarea (shown when custom style is enabled) %>
        <div data-lyrics-options-target="styleTextareaContainer" class="hidden">
          <%= render 'shared/textarea_input',
                     label_text: t('.style'),
                     input_id: 'song_style',
                     input_name: 'generation_task[style]',
                     rows_count: 3,
                     placeholder_text: t('.style_placeholder'),
                     character_limit: 500,
                     textarea_html_attributes: {
                      data: {
                        "lyrics-options-target": "styleTextarea"
                      }
                     } %>
        </div>

        <%# Default Components (shown when custom style is disabled) %>
        <div data-lyrics-options-target="defaultComponents" class="space-y-6 lg:space-y-4">

          <%= render 'shared/searchable_tag_input',
                     label_text: t('.genre'),
                     section_id: 'lyrics_genre',
                     input_name: 'generation_task[genres][]',
                     placeholder_text: t('.genre_placeholder'),
                     quick_add_tags: t('generation_tasks.quick_add_tags.genres'),
                     category: 'genre' %>

          <%= render 'shared/searchable_tag_input',
                     label_text: t('.moods'),
                     section_id: 'lyrics_moods',
                     input_name: 'generation_task[moods][]',
                     placeholder_text: t('.moods_placeholder'),
                     quick_add_tags: t('generation_tasks.quick_add_tags.moods'),
                     category: 'mood' %>

          <%= render 'shared/selectable_tag_group',
                     label_text: t('.vocal'),
                     input_name_prefix: 'generation_task[vocals]',
                     html_id_context: 'lyrics_vocals',
                     wrapper_data_attributes: { 'data-lyrics-options-target': 'vocalGroup' },
                     tags: [
                       { value: 'male_voice', display_text: t('.male_voice'), data_action: 'change->lyrics-options#vocalSelectionChanged' },
                       { value: 'female_voice', display_text: t('.female_voice'), is_selected_by_default: true, data_action: 'change->lyrics-options#vocalSelectionChanged' },
                       { value: 'instrumental', display_text: t('.instrumental'), data_action: 'change->lyrics-options#vocalSelectionChanged' }
                     ] %>

          <%# Advanced options collapsible %>
          <%= render 'shared/collapsible_section', show_text: t('.show_more_options'), hide_text: t('.hide_advanced_options') do %>
            <%= render "shared/selectable_tag_group",
                       label_text: t('generation_tasks.generator_interface.tempo'),
                       input_name_prefix: "generation_task[tempo]",
                       html_id_context: "lyrics_tempo",
                       tags: [
                         { value: "60-80", display_text: t('generation_tasks.generator_interface.tempo_slow'), is_selected_by_default: true },
                         { value: "80-120", display_text: t('generation_tasks.generator_interface.tempo_medium') },
                         { value: "120-160", display_text: t('generation_tasks.generator_interface.tempo_fast') }
                       ] %>

            <%= render "shared/searchable_tag_input",
                       label_text: t('generation_tasks.generator_interface.instrument'),
                       section_id: "lyrics_instrument",
                       input_name: "generation_task[instruments][]",
                       placeholder_text: t('generation_tasks.generator_interface.instrument_placeholder'),
                       quick_add_tags: t('generation_tasks.quick_add_tags.instruments'),
                       category: "instrument" %>

            <%# TODO: Add duration back in %>
            <%#= render "shared/selectable_tag_group",
                       label_text: t('generation_tasks.generator_interface.duration'),
                       input_name_prefix: "generation_task[duration]",
                       html_id_context: "lyrics_duration",
                       tags: [
                         { value: "30s",  display_text: t('generation_tasks.generator_interface.duration_30_s') },
                         { value: "60s",  display_text: t('generation_tasks.generator_interface.duration_60_s') },
                         { value: "1min", display_text: t('generation_tasks.generator_interface.duration_1_min') },
                         { value: "4min", display_text: t('generation_tasks.generator_interface.duration_4_min'), is_premium: true, is_selected_by_default: true }
                       ] %>
          <% end %>
        </div>

      </div>
    </div>

    <!-- Button section, fixed at the bottom of the form -->
    <div class="text-center p-6 lg:p-4 flex-shrink-0">
      <% if current_user %>
        <%= form.submit t('.create_music'), class: "w-full text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:bg-gradient-to-l focus:ring-4 focus:outline-none focus:ring-purple-200 dark:focus:ring-purple-800 font-medium rounded-lg text-xl lg:text-lg px-6 lg:px-5 py-4 lg:py-3 text-center cursor-pointer", data: { guest_validation_target: "submitButton" } %>
        <p class="mt-3 lg:mt-2 text-sm lg:text-xs text-slate-400"><%= t('.cost') %></p>
      <% else %>
        <button type="button"
                data-guest-validation-target="submitButton"
                data-fake-song-target="submitButton"
                data-action="click->form-persistence#saveState click->fake-song#showFakeWorkspace"
                class="w-full text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:bg-gradient-to-l focus:ring-4 focus:outline-none focus:ring-purple-200 dark:focus:ring-purple-800 font-medium rounded-lg text-xl lg:text-lg px-6 lg:px-5 py-4 lg:py-3 text-center cursor-pointer">
          <%= t('.create_music') %>
        </button>
      <% end %>
    </div>
  <% end %>

  <%# Hidden form for AI stream generation %>
  <%= form_with(url: "#", method: :post, html: { "data-turbo-stream": true, "data-openai-stream-target": "form", class: "hidden" }) do |f| %>
    <%= f.hidden_field :target_id, value: "", "data-openai-stream-target": "targetIdInput" %>
  <% end %>
</div>
