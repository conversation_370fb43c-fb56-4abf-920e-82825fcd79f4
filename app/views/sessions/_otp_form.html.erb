<%# app/views/sessions/_otp_form.html.erb %>

<div id="otp-form-wrapper">
  <%= form_with model: form, url: verify_otp_sessions_path, method: :post, data: { turbo_frame: "_top" } do |f| %>
    <%= f.hidden_field :email, value: email %> <%# `email` local should still be passed to this partial %>

    <%= turbo_frame_tag "auth_flash_messages" %>

    <div class="mb-4">
      <%= f.label :otp, t('sessions.new.login_code'), class: "block mb-2 text-sm font-medium text-white" %>
      <%= f.text_field :otp, required: true, autofocus: true, autocomplete: "one-time-code", inputmode: "numeric", pattern: "[0-9]*", class: "bg-gray-600 border border-gray-500 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 placeholder-gray-300 text-center font-mono tracking-widest" %>
    </div>

    <div class="mt-4">
      <%= f.submit t('sessions.new.verify_otp_and_login'), class: "w-full text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center cursor-pointer" %>
    </div>
  <% end %>

  <%# Resend OTP section - now a dedicated form for Turbo %>
  <%= form_with url: request_otp_sessions_path, method: :post, class: "mt-4 text-center" do |f| %>
    <%= f.hidden_field :email, name: "otp_virtual_form[email]", value: email %>
    <%= f.hidden_field :resend, name: "otp_virtual_form[resend]", value: "true" %>

    <div data-controller="otp-countdown"
         data-otp-countdown-expire-in-value="<%= OtpVirtualForm::OTP_RESEND_COOLDOWN %>"
         data-otp-countdown-i18n-value="<%= {
           resendIn: t('sessions.new.resend_in_html'),
           resendCode: t('sessions.new.resend_code'),
           resendFailed: t('sessions.new.resend_failed')
         }.to_json %>">

      <button type="submit"
              data-otp-countdown-target="button"
              class="text-sm font-medium text-blue-500 hover:underline disabled:opacity-50 disabled:cursor-not-allowed">
        <%= t('sessions.new.resend_code') %>
      </button>
    </div>
  <% end %>
</div>
