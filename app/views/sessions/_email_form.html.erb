<%# app/views/sessions/_email_form.html.erb %>

<%= form_with model: form, url: request_otp_sessions_path, method: :post, data: { controller: "email-validation" } do |f| %>
  <% if f.object.errors.any? %>
    <div class="my-3 p-3 text-sm text-red-400 bg-red-900/20 rounded-lg border border-red-800" role="alert">
      <ul class="list-disc list-inside space-y-1">
        <% f.object.errors.full_messages.each do |message| %>
          <li class="font-medium"><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="mb-4">
    <%= f.label :email, t('sessions.new.your_email'), class: "block mb-2 text-sm font-medium text-white" %>
    <%= f.email_field :email,
        required: true,
        autofocus: true,
        data: {
          email_validation_target: "emailInput",
          action: "input->email-validation#validateEmail blur->email-validation#validateEmail"
        },
        class: "bg-gray-600 border border-gray-500 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 placeholder-gray-300",
        placeholder: "<EMAIL>" %>
    <div data-email-validation-target="errorMessage" class="hidden mt-2 text-sm text-red-400">
      <%= t('sessions.new.invalid_email') %>
    </div>
  </div>

  <div class="mt-4">
    <%= f.submit t('sessions.new.continue_with_email'),
        data: { email_validation_target: "submitButton" },
        class: "w-full text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed" %>
  </div>
<% end %>
