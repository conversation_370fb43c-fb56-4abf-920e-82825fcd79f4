<%# app/views/sessions/request_otp.turbo_stream.erb %>
<% if params.dig(:otp_virtual_form, :resend) == "true" %>
  <%# This is a resend request. We only need to update the flash message. %>
  <%= turbo_stream.update "auth_flash_messages" do %>
    <% flash.now[:notice] = t("auth.verification_code_resent") %>
    <%= render partial: "shared/simple_flash_alert" %>
  <% end %>
<% else %>
  <%# This is the initial OTP request. Replace the form and show the initial message. %>
  <%= turbo_stream.replace "auth_form_container" do %>
    <%= render partial: "sessions/otp_form", locals: { email: session[:otp_email], form: OtpVirtualForm.new(email: session[:otp_email], token: session[:otp_token], form_type: :verify) } %>
  <% end %>

  <%= turbo_stream.update "auth_flash_messages" do %>
    <% flash.now[:notice] = t("auth.verification_code_sent", email: session[:otp_email]) %>
    <%= render partial: "shared/simple_flash_alert" %>
  <% end %>
<% end %>
