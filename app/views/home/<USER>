<%# -------------------------------------------------------
     generator_classes: visible for members on mobile,
                        hidden for guests on mobile,
                        always visible ≥ lg
-------------------------------------------------------- %>
<% generator_classes =
     if current_user
       "block w-full lg:w-1/3 lg:overflow-y-auto"
     else
       "hidden lg:block lg:w-1/3 lg:overflow-y-auto"
     end
%>

<%# ---------- SINGLE WRAPPER: column on mobile, row on desktop ---------- %>
<div class="flex flex-col lg:flex-row gap-4 lg:gap-4 h-full" data-controller="fake-song">

  <%# ----- Generator interface (rendered once) ----- %>
  <div class="<%= generator_classes %>">
    <%= render 'generation_tasks/generator_interface' %>
  </div>

  <%# ----- Right-hand content (intro or workspace) ----- %>
  <% if current_user %>
    <div class="w-full lg:w-2/3 lg:overflow-y-auto">
      <%= render 'shared/music_workspace',
                 songs: @songs,
                 tasks_in_progress: @tasks_in_progress,
                 first_song: @first_song,
                 show_create_first: @songs.empty? %>
    </div>
  <% else %>
    <div class="w-full lg:w-2/3 lg:overflow-y-auto" data-fake-song-target="introduction">
      <%= render 'pages/introduction' %>
    </div>
    <div class="w-full lg:w-2/3 lg:overflow-y-auto hidden" data-fake-song-target="workspace">
      <%= render 'shared/music_workspace',
                 songs: [],
                 tasks_in_progress: [],
                 first_song: nil,
                 show_create_first: true %>
    </div>
  <% end %>

</div>
