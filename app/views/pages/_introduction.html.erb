<!-- Hero Section -->
<section class="relative min-h-screen bg-gradient-to-br from-slate-900 via-purple-900/30 to-blue-900/30 overflow-hidden flex items-center"
         data-controller="cursor-track">
  <!-- Enhanced Background effects -->
  <div class="absolute inset-0">
    <!-- Primary gradient overlay -->
    <div class="absolute inset-0 bg-gradient-to-br from-slate-900/90 via-purple-900/40 to-blue-900/40"></div>

    <!-- Animated gradient orbs -->
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse"></div>
    <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse delay-1000"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-full mix-blend-multiply filter blur-3xl opacity-50 animate-pulse delay-2000"></div>

    <!-- Floating particles effect -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute top-1/4 left-1/3 w-2 h-2 bg-white/20 rounded-full animate-bounce delay-300"></div>
      <div class="absolute top-2/3 left-1/4 w-1 h-1 bg-purple-400/30 rounded-full animate-bounce delay-700"></div>
      <div class="absolute top-1/2 right-1/3 w-3 h-3 bg-blue-400/20 rounded-full animate-bounce delay-1000"></div>
      <div class="absolute bottom-1/3 right-1/4 w-2 h-2 bg-pink-400/30 rounded-full animate-bounce delay-500"></div>
    </div>

    <!-- Cursor tracking effect -->
    <div class="absolute inset-0 pointer-events-none opacity-0 transition-opacity duration-300"
         data-cursor-track-target="glow"
         style="background: radial-gradient(600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(147, 51, 234, 0.15), transparent 40%);">
    </div>
  </div>

  <div class="relative max-w-7xl mx-auto px-4 py-24 sm:px-6 lg:px-8 z-10">
    <div class="text-center">
      <!-- Enhanced title with better gradient and animation -->
      <h1 class="text-6xl md:text-7xl font-black mb-8 leading-tight">
        <span class="bg-gradient-to-r from-blue-400 via-purple-400 via-pink-400 to-indigo-400 bg-clip-text text-transparent animate-pulse">
          <%= @page&.field_value("hero_title") %>
        </span>
      </h1>

      <div class="relative mb-16 max-w-5xl mx-auto">
        <p class="hero-subtitle text-2xl md:text-3xl leading-relaxed font-medium tracking-tight">
          <span class="hero-subtitle-container">
            <span class="hero-subtitle-text">
              <%= @page&.field_value("hero_subtitle") %>
            </span>
          </span>
        </p>
        <!-- Floating accent elements -->
        <div class="hero-accent hero-accent-1"></div>
        <div class="hero-accent hero-accent-2"></div>
        <div class="hero-accent hero-accent-3"></div>
      </div>

      <!-- Main CTA Button -->
      <div class="mb-20">
        <%= link_to login_path,
                    data: { turbo_frame: "modal_frame" },
                    class: "group relative inline-flex items-center justify-center px-12 py-6 text-xl font-bold text-white bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 rounded-2xl hover:from-purple-700 hover:via-pink-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-500 shadow-2xl hover:shadow-purple-500/40 cursor-pointer" do %>
          <div class="absolute inset-0 bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-500"></div>
          <div class="relative flex items-center">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM21 16c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2z"></path>
            </svg>
            <%= @page&.field_value("hero_cta_main") %>
          </div>
        <% end %>
      </div>

      <!-- Enhanced Stats with better styling -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto">
        <div class="group text-center p-6 rounded-2xl bg-white/5 border border-white/10 backdrop-blur-sm hover:bg-white/10 transition-all duration-300">
          <div class="text-4xl font-black text-purple-400 mb-2 group-hover:scale-110 transition-transform duration-300">50K+</div>
          <div class="text-slate-300 font-medium"><%= @page&.field_value("hero_stat_songs_created") %></div>
        </div>
        <div class="group text-center p-6 rounded-2xl bg-white/5 border border-white/10 backdrop-blur-sm hover:bg-white/10 transition-all duration-300">
          <div class="text-4xl font-black text-blue-400 mb-2 group-hover:scale-110 transition-transform duration-300">100+</div>
          <div class="text-slate-300 font-medium"><%= @page&.field_value("hero_stat_genres_available") %></div>
        </div>
        <div class="group text-center p-6 rounded-2xl bg-white/5 border border-white/10 backdrop-blur-sm hover:bg-white/10 transition-all duration-300">
          <div class="text-4xl font-black text-pink-400 mb-2 group-hover:scale-110 transition-transform duration-300">4 <%= @page&.field_value("hero_stat_average_generation_unit") %></div>
          <div class="text-slate-300 font-medium"><%= @page&.field_value("hero_stat_average_generation") %></div>
        </div>
        <div class="group text-center p-6 rounded-2xl bg-white/5 border border-white/10 backdrop-blur-sm hover:bg-white/10 transition-all duration-300">
          <div class="text-4xl font-black text-green-400 mb-2 group-hover:scale-110 transition-transform duration-300">100%</div>
          <div class="text-slate-300 font-medium"><%= @page&.field_value("hero_stat_to_get_started") %></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scroll indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
    <svg class="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
    </svg>
  </div>
</section>

<!-- App Function Section -->
<section class="py-24 bg-slate-800/50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
        <%= @page&.field_value("function_title") %>
      </h2>
      <p class="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
        <%= @page&.field_value("function_subtitle") %>
      </p>
      <p class="text-lg text-slate-400 max-w-4xl mx-auto leading-relaxed">
        <%= @page&.field_value("function_description") %>
      </p>
    </div>

    <!-- Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="group p-6 bg-gradient-to-br from-purple-600/10 to-purple-800/10 rounded-2xl border border-purple-500/20 hover:border-purple-400/40 transition-all duration-300 hover:transform hover:scale-105">
        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-white mb-3">
          <%= @page&.field_value("feature_ai_powered_title") %>
        </h3>
        <p class="text-slate-400">
          <%= @page&.field_value("feature_ai_powered_desc") %>
        </p>
      </div>

      <div class="group p-6 bg-gradient-to-br from-blue-600/10 to-blue-800/10 rounded-2xl border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 hover:transform hover:scale-105">
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM21 16c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-white mb-3">
          <%= @page&.field_value("feature_multiple_genres_title") %>
        </h3>
        <p class="text-slate-400">
          <%= @page&.field_value("feature_multiple_genres_desc") %>
        </p>
      </div>

      <div class="group p-6 bg-gradient-to-br from-pink-600/10 to-pink-800/10 rounded-2xl border border-pink-500/20 hover:border-pink-400/40 transition-all duration-300 hover:transform hover:scale-105">
        <div class="w-12 h-12 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-white mb-3">
          <%= @page&.field_value("feature_custom_lyrics_title") %>
        </h3>
        <p class="text-slate-400">
          <%= @page&.field_value("feature_custom_lyrics_desc") %>
        </p>
      </div>

      <div class="group p-6 bg-gradient-to-br from-green-600/10 to-green-800/10 rounded-2xl border border-green-500/20 hover:border-green-400/40 transition-all duration-300 hover:transform hover:scale-105">
        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-white mb-3">
          <%= @page&.field_value("feature_instant_results_title") %>
        </h3>
        <p class="text-slate-400">
          <%= @page&.field_value("feature_instant_results_desc") %>
        </p>
      </div>
    </div>
  </div>
</section>

<!-- How It Works Section -->
<section class="py-24 bg-slate-900">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
        <%= @page&.field_value("how_it_works_title") %>
      </h2>
      <p class="text-xl text-slate-300 max-w-3xl mx-auto">
        <%= @page&.field_value("how_it_works_subtitle") %>
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
      <!-- Step 1 -->
      <div class="relative text-center group">
        <div class="relative mb-8">
          <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto shadow-2xl group-hover:scale-110 transition-transform duration-300">
            <span class="text-2xl font-bold text-white">1</span>
          </div>
          <div class="absolute top-1/2 left-full transform -translate-y-1/2 hidden lg:block">
            <svg class="w-24 h-8 text-purple-500/30" fill="none" stroke="currentColor" viewBox="0 0 100 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h90m-5-5l5 5-5 5"></path>
            </svg>
          </div>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">
          <%= @page&.field_value("step_1_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("step_1_desc") %>
        </p>
      </div>

      <!-- Step 2 -->
      <div class="relative text-center group">
        <div class="relative mb-8">
          <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto shadow-2xl group-hover:scale-110 transition-transform duration-300">
            <span class="text-2xl font-bold text-white">2</span>
          </div>
          <div class="absolute top-1/2 left-full transform -translate-y-1/2 hidden lg:block">
            <svg class="w-24 h-8 text-blue-500/30" fill="none" stroke="currentColor" viewBox="0 0 100 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h90m-5-5l5 5-5 5"></path>
            </svg>
          </div>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">
          <%= @page&.field_value("step_2_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("step_2_desc") %>
        </p>
      </div>

      <!-- Step 3 -->
      <div class="relative text-center group">
        <div class="relative mb-8">
          <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto shadow-2xl group-hover:scale-110 transition-transform duration-300">
            <span class="text-2xl font-bold text-white">3</span>
          </div>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">
          <%= @page&.field_value("step_3_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("step_3_desc") %>
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Empowers Creators Section -->
<section class="py-24 bg-slate-800/50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
        <%= @page&.field_value("empowers_title") %>
      </h2>
      <p class="text-xl text-slate-300 max-w-3xl mx-auto">
        <%= @page&.field_value("empowers_subtitle") %>
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Content Creators -->
      <div class="group p-8 bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-slate-700 hover:border-purple-500/40 transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-purple-500/10">
        <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-white mb-4">
          <%= @page&.field_value("creator_content_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("creator_content_desc") %>
        </p>
      </div>

      <!-- Filmmakers -->
      <div class="group p-8 bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-slate-700 hover:border-blue-500/40 transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-blue-500/10">
        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 3v10a2 2 0 002 2h6a2 2 0 002-2V7m-8 0V5a2 2 0 012-2h4a2 2 0 012 2v2m-8 0h8"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-white mb-4">
          <%= @page&.field_value("creator_filmmaker_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("creator_filmmaker_desc") %>
        </p>
      </div>

      <!-- Game Developers -->
      <div class="group p-8 bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-slate-700 hover:border-green-500/40 transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-green-500/10">
        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-white mb-4">
          <%= @page&.field_value("creator_gamer_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("creator_gamer_desc") %>
        </p>
      </div>

      <!-- Musicians -->
      <div class="group p-8 bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-slate-700 hover:border-purple-500/40 transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-purple-500/10">
        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM21 16c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-white mb-4">
          <%= @page&.field_value("creator_musician_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("creator_musician_desc") %>
        </p>
      </div>

      <!-- Businesses -->
      <div class="group p-8 bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-slate-700 hover:border-yellow-500/40 transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-yellow-500/10">
        <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-white mb-4">
          <%= @page&.field_value("creator_business_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("creator_business_desc") %>
        </p>
      </div>

      <!-- Educators -->
      <div class="group p-8 bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-slate-700 hover:border-teal-500/40 transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-teal-500/10">
        <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-blue-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-white mb-4">
          <%= @page&.field_value("creator_educator_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("creator_educator_desc") %>
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Unique Value Section -->
<section class="py-24 bg-slate-900">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
        <%= @page&.field_value("unique_value_title") %>
      </h2>
      <p class="text-xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
        <%= @page&.field_value("unique_value_subtitle") %>
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="text-center group">
        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-2xl">
          <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-white mb-4">
          <%= @page&.field_value("value_free_credits_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("value_free_credits_desc") %>
        </p>
      </div>

      <div class="text-center group">
        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-2xl">
          <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-white mb-4">
          <%= @page&.field_value("value_commercial_use_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("value_commercial_use_desc") %>
        </p>
      </div>

      <div class="text-center group">
        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-2xl">
          <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-white mb-4">
          <%= @page&.field_value("value_no_experience_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("value_no_experience_desc") %>
        </p>
      </div>

      <div class="text-center group">
        <div class="w-20 h-20 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-2xl">
          <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-white mb-4">
          <%= @page&.field_value("value_unlimited_creativity_title") %>
        </h3>
        <p class="text-slate-400 leading-relaxed">
          <%= @page&.field_value("value_unlimited_creativity_desc") %>
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="py-24 bg-slate-800/50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
        <%= @page&.field_value("testimonials_title") %>
      </h2>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Testimonial 1 -->
      <div class="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-8 border border-slate-700 hover:border-purple-500/40 transition-all duration-300">
        <div class="flex items-center mb-6">
          <div class="flex text-yellow-400">
            <% 5.times do %>
              <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            <% end %>
          </div>
        </div>
        <blockquote class="text-slate-300 text-lg mb-6 leading-relaxed">
          "<%= @page&.field_value("testimonial_1_text") %>"
        </blockquote>
        <div class="flex items-center">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4">
            <span class="text-white font-bold text-lg">S</span>
          </div>
          <div>
            <div class="text-white font-semibold">
              <%= @page&.field_value("testimonial_1_author") %>
            </div>
            <div class="text-slate-400 text-sm">
              <%= @page&.field_value("testimonial_1_role") %>
            </div>
          </div>
        </div>
      </div>

      <!-- Testimonial 2 -->
      <div class="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-8 border border-slate-700 hover:border-blue-500/40 transition-all duration-300">
        <div class="flex items-center mb-6">
          <div class="flex text-yellow-400">
            <% 5.times do %>
              <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            <% end %>
          </div>
        </div>
        <blockquote class="text-slate-300 text-lg mb-6 leading-relaxed">
          "<%= @page&.field_value("testimonial_2_text") %>"
        </blockquote>
        <div class="flex items-center">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-teal-500 rounded-full flex items-center justify-center mr-4">
            <span class="text-white font-bold text-lg">M</span>
          </div>
          <div>
            <div class="text-white font-semibold">
              <%= @page&.field_value("testimonial_2_author") %>
            </div>
            <div class="text-slate-400 text-sm">
              <%= @page&.field_value("testimonial_2_role") %>
            </div>
          </div>
        </div>
      </div>

      <!-- Testimonial 3 -->
      <div class="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-8 border border-slate-700 hover:border-green-500/40 transition-all duration-300">
        <div class="flex items-center mb-6">
          <div class="flex text-yellow-400">
            <% 5.times do %>
              <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            <% end %>
          </div>
        </div>
        <blockquote class="text-slate-300 text-lg mb-6 leading-relaxed">
          "<%= @page&.field_value("testimonial_3_text") %>"
        </blockquote>
        <div class="flex items-center">
          <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-4">
            <span class="text-white font-bold text-lg">E</span>
          </div>
          <div>
            <div class="text-white font-semibold">
              <%= @page&.field_value("testimonial_3_author") %>
            </div>
            <div class="text-slate-400 text-sm">
              <%= @page&.field_value("testimonial_3_role") %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-24 bg-slate-900">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
        <%= @page&.field_value("faq_title") %>
      </h2>
    </div>

    <div class="space-y-6" data-controller="accordion">
      <% (1..6).each do |faq_number| %>
        <%= render "shared/faq_item", faq_number: faq_number %>
      <% end %>
    </div>
  </div>
</section>

<!-- Footer Section -->
<footer class="bg-slate-900 border-t border-slate-800">
  <div class="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:px-8">
    <div class="flex flex-col md:flex-row items-center justify-between">
      <div class="mb-8 md:mb-0">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM21 16c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2z"></path>
            </svg>
          </div>
          <span class="text-2xl font-bold text-white"><%= @page&.field_value("footer_brand_name") %></span>
        </div>
        <p class="text-slate-400 mt-4 max-w-sm">
          <%= @page&.field_value("footer_description") %>
        </p>
      </div>

      <div class="flex flex-wrap justify-center md:justify-end gap-8">
        <%= link_to privacy_policy_path, class: "text-slate-400 hover:text-white transition-colors duration-300" do %>
          <%= @page&.field_value("footer_privacy") %>
        <% end %>
        <%= link_to terms_of_service_path, class: "text-slate-400 hover:text-white transition-colors duration-300" do %>
          <%= @page&.field_value("footer_terms") %>
        <% end %>
        <button type="button"
                data-controller="clipboard"
                data-clipboard-text-value="<EMAIL>"
                data-action="clipboard#copy"
                data-clipboard-message-value="<%= t('layouts.sidebar.email_copied', email: '<EMAIL>') %>"
                class="text-slate-400 hover:text-white transition-colors duration-300 cursor-pointer">
          <%= @page&.field_value("footer_support") %>
        </button>
        <button type="button"
                data-controller="clipboard"
                data-clipboard-text-value="<EMAIL>"
                data-action="clipboard#copy"
                data-clipboard-message-value="<%= t('layouts.sidebar.email_copied', email: '<EMAIL>') %>"
                class="text-slate-400 hover:text-white transition-colors duration-300 cursor-pointer">
          <%= @page&.field_value("footer_contact") %>
        </button>
      </div>
    </div>

    <div class="mt-8 pt-8 border-t border-slate-800 text-center">
      <p class="text-slate-400">
        © <%= Date.current.year %> <%= @page&.field_value("footer_brand_name") %>. <%= @page&.field_value("footer_copyright") %>
      </p>
    </div>
  </div>
</footer>
