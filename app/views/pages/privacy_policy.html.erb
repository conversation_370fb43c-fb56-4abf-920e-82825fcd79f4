<div class="min-h-screen bg-slate-900">
  <div class="max-w-4xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
        <%= @page.field_value("title") %>
      </h1>
    </div>

    <div class="bg-slate-800/50 rounded-2xl border border-slate-700 p-8 md:p-12">
      <div class="prose prose-slate prose-lg max-w-none">
        <p class="text-slate-300 leading-relaxed mb-8">
          <%= @page.field_value("intro") %>
        </p>

        <% %w[information_collection information_usage cookie_usage information_sharing information_security opt_out payment_processing eea_residents other_important contact].each do |section| %>
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-white mb-4">
              <%= @page.field_value("#{section}_title") %>
            </h2>
            <p class="text-slate-300 leading-relaxed">
              <%= @page.field_value("#{section}_content") %>
            </p>
          </div>
        <% end %>
      </div>
    </div>

    <div class="text-center mt-12">
      <%= link_to root_path,
          class: "inline-flex items-center px-6 py-3 text-lg font-medium text-slate-300 hover:text-white transition-colors duration-300" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <%= t('shared.back_to_home') %>
      <% end %>
    </div>
  </div>
</div>
