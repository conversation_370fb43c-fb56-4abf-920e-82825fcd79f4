<%# app/views/shared/_flash_message_error.html.erb %>
<div class="flex items-center w-full max-w-xl p-4 mb-4 rounded-lg shadow-sm bg-gray-800" data-toast-target="toast">
  <div class="inline-flex items-center justify-center shrink-0 w-8 h-8 rounded-lg text-red-200 bg-red-800">
    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z"/>
    </svg>
    <span class="sr-only"><%= t('flash.shared.error') %></span>
  </div>
  <div class="ms-3 text-sm font-normal text-red-300" data-toast-target="messageContainer">
    <%= flash_messages.to_s.html_safe %>
  </div>
  <div class="flex items-center ml-4">
    <button type="button"
            data-action="click->toast#close"
            class="-mx-1.5 -my-1.5 bg-gray-800 text-gray-500 hover:text-white rounded-lg focus:ring-2 focus:ring-red-600 p-1.5 inline-flex items-center justify-center h-8 w-8 hover:bg-red-700"
            aria-label="<%= t('shared.close') %>">
      <span class="sr-only"><%= t('shared.close') %></span>
      <svg class="w-3 h-3" stroke="currentColor" fill="none" viewBox="0 0 14 14">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
      </svg>
    </button>
  </div>
</div>
