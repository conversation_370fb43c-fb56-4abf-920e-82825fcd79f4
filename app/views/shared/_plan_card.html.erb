<% user ||= current_user %>
<% if user %>
  <%
    # Plan configuration based on plan_type
    plan_config = case user.plan_type
    when 'free'
      {
        name: t('.free_plan'),
        credit_used: user.plan_used,
        credit_total: user.plan_limit,
        credit_label: t('.todays_usage'),
        credit_type: :limited,
        button_text: t('.upgrade_plan'),
        button_icon: :upgrade
      }
    when 'basic', 'pro'
      {
        name: t(".#{user.plan_type}_plan"),
        credit_used: user.plan_used,
        credit_total: user.plan_limit,
        credit_label: t('.plan_usage'),
        credit_type: :limited,
        button_text: t('.buy_more_credits'),
        button_icon: :credits
      }
    when 'unlimited'
      if user.plan_limit == -1
        {
          name: t('.unlimited_plan'),
          credit_type: :unlimited,
          button_text: t('.buy_more_credits'),
          button_icon: :credits

        }
      else
        {
          name: t('.unlimited_plan'),
          credit_used: user.plan_used,
          credit_total: user.plan_limit,
          credit_label: t('.plan_usage'),
          credit_type: :limited,
          button_text: t('.buy_more_credits'),
          button_icon: :credits
        }
      end
    else
      {
        name: t(".#{user.plan_type}_plan"),
        credit_used: user.plan_used,
        credit_total: user.plan_limit,
        credit_label: t('.plan_usage'),
        credit_type: :limited,
        button_text: t('.buy_more_credits'),
        button_icon: :credits
      }
    end

    # Check if user has purchased credits to display
    has_purchased_credits = user.purchased_credits > 0
  %>

  <%= turbo_stream_from user, :plan_card %>
  <div id="plan_card_<%= user.id %>" class="mt-4 p-4 bg-gradient-to-br from-green-400 to-blue-600 rounded-lg shadow-lg">
    <!-- Plan Type Badge -->
    <div class="flex items-center justify-between mb-3">
      <span class="text-xs font-semibold px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white uppercase tracking-wide">
        <%= plan_config[:name] %>
      </span>
    </div>

    <!-- Plan Credit Usage -->
    <div class="mb-3">
      <% if plan_config[:credit_type] == :unlimited %>
        <!-- Unlimited Credits Display -->
        <div class="flex items-center justify-between text-white/90 text-sm mb-1">
          <span><%= t('.credits') %></span>
          <div class="flex items-center gap-1">
            <svg class="w-4 h-4 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
            <span class="font-medium"><%= t('.unlimited') %></span>
          </div>
        </div>
        <div class="w-full bg-white/20 rounded-full h-2">
          <div class="bg-gradient-to-r from-yellow-300 to-yellow-500 h-2 rounded-full w-full animate-pulse"></div>
        </div>
      <% else %>
        <!-- Plan Credits Display -->
        <div class="flex items-center justify-between text-white/90 text-sm mb-1">
          <span><%= plan_config[:credit_label] %></span>
          <span class="font-medium"><%= plan_config[:credit_used] %>/<%= plan_config[:credit_total] %></span>
        </div>
        <div class="w-full bg-white/20 rounded-full h-2">
          <% usage_percentage = plan_config[:credit_total] > 0 ? (plan_config[:credit_used].to_f / plan_config[:credit_total] * 100).round(1) : 0 %>
          <div class="bg-white h-2 rounded-full transition-all duration-300" style="width: <%= [usage_percentage, 100].min %>%"></div>
        </div>
      <% end %>
    </div>

    <!-- Purchased Credits Usage (if any) -->
    <% if has_purchased_credits %>
      <div class="mb-3">
        <div class="flex items-center justify-between text-white/90 text-sm mb-1">
          <span><%= t('.purchased_credits') %></span>
          <span class="font-medium"><%= user.purchased_credits_used %>/<%= user.purchased_credits %></span>
        </div>
        <div class="w-full bg-white/20 rounded-full h-2">
          <% purchased_usage_percentage = user.purchased_credits > 0 ? (user.purchased_credits_used.to_f / user.purchased_credits * 100).round(1) : 0 %>
          <div class="bg-gradient-to-r from-purple-300 to-purple-500 h-2 rounded-full transition-all duration-300" style="width: <%= [purchased_usage_percentage, 100].min %>%"></div>
        </div>
      </div>
    <% end %>

    <!-- Action Button -->
    <%
      target_path = case plan_config[:button_icon]
                    when :credits
                      credits_pricing_index_path
                    when :upgrade, :savings, :settings
                      pricing_index_path
                    else
                      pricing_index_path
                    end
    %>
    <%= link_to target_path,
        class: "block w-full text-center py-2 px-4 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white font-medium rounded-lg transition-all duration-200 hover:scale-105 text-sm cursor-pointer" do %>
      <div class="flex items-center justify-center gap-2">
        <% case plan_config[:button_icon] %>
        <% when :upgrade %>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
          </svg>
        <% when :credits %>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
          </svg>
        <% when :savings %>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
          </svg>
        <% when :settings %>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
        <% end %>
        <%= plan_config[:button_text] %>
      </div>
    <% end %>
  </div>
<% end %>
