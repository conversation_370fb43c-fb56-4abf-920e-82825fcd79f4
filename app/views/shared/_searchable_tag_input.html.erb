<%# Locals:
  label_text: String,
  section_id: String (for unique IDs, e.g., 'description_genre'),
  input_name: String (for hidden inputs, e.g., 'generation_task[genres][]'),
  placeholder_text: String,
  quick_add_tags: Array of strings (optional),
  category: String (optional, e.g., 'genre', 'mood', 'instrument')
%>
<% quick_add_tags_val = local_assigns.fetch(:quick_add_tags, []) %>
<% category_val = local_assigns[:category] %>
<% search_input_id = "#{section_id}_search_input" %>
<% suggestions_container_id = "#{section_id}_suggestions_container" %>

<div class="mb-4"
  data-controller="searchable-tag-input"
  data-searchable-tag-input-input-name-value="<%= input_name %>"
  data-searchable-tag-input-original-placeholder-value="<%= placeholder_text %>"
  data-searchable-tag-input-locale-value="<%= I18n.locale %>"
  <% if category_val.present? %>data-searchable-tag-input-category-value="<%= category_val %>"<% end %>>

  <label for="<%= search_input_id %>" class="block mb-1 text-sm font-medium text-slate-300"><%= label_text %></label>

  <div class="relative">
    <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
      <%= image_tag "/search_icon.svg", class: "h-4 w-4 text-gray-400" %>
    </div>
    <div data-searchable-tag-input-target="selectedTagsContainer"
        class="flex flex-wrap items-center gap-2 w-full p-2 ps-10 text-sm border rounded-lg bg-slate-700 border-slate-600 min-h-[42px]">
      <input type="text"
            id="<%= search_input_id %>"
            data-searchable-tag-input-target="searchInput"
            placeholder="<%= placeholder_text %>"
            data-action="
              input->searchable-tag-input#handleInput
              keydown->searchable-tag-input#handleBackspace
              keydown->searchable-tag-input#addTagFromInputOrSelect
              keydown->searchable-tag-input#navigateSuggestions
              focus->searchable-tag-input#handleInput
            "
            autocomplete="off"
            class="flex-1 bg-transparent placeholder-gray-400 text-white text-sm border-0 focus:ring-0 focus:outline-none p-0 min-w-0 overflow-hidden text-ellipsis whitespace-nowrap">
    </div>
    <turbo-frame id="<%= suggestions_container_id %>"
                 data-searchable-tag-input-target="suggestionsDropdown"
                 class="block w-full">
    </turbo-frame>
  </div>

  <div data-searchable-tag-input-target="hiddenInputsContainer" class="hidden"></div>

  <% if quick_add_tags_val.any? %>
    <div data-searchable-tag-input-target="quickAddContainer" class="mt-2 flex flex-wrap gap-1 text-sm justify-start">
      <% quick_add_tags_val.each do |tag| %>
        <button type="button"
                class="px-1.5 py-0.5 bg-slate-600 hover:bg-slate-500 text-slate-300 rounded cursor-pointer"
                data-action="click->searchable-tag-input#addTag"
                data-tag-value="<%= tag %>">
          <%= tag %>
        </button>
      <% end %>
    </div>
  <% end %>
</div>
