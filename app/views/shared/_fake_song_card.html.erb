<div class="fake-song-card relative rounded-xl shadow-2xl p-6 transition-all duration-500 ease-out mb-4 overflow-hidden bg-gradient-to-br from-blue-800 via-purple-700 to-indigo-800"
     data-controller="fake-song">

  <!-- Strong Gradient Background -->
  <div class="absolute inset-0 bg-gradient-to-br from-blue-700/90 via-purple-600/80 to-indigo-700/90"></div>
  <div class="absolute inset-0 bg-gradient-to-t from-purple-950/50 to-transparent"></div>

  <!-- Heavy Liquid Glass Overlay that makes everything blurry -->
  <div class="absolute inset-0 rounded-xl overflow-hidden pointer-events-none z-10">
    <!-- Main heavy glass layer -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-200/20 via-purple-200/15 to-indigo-200/10 backdrop-blur-[8px] rounded-xl"></div>

    <!-- Multiple shimmer layers -->
    <div class="absolute inset-0 rounded-xl">
      <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-blue-300/15 to-transparent transform -skew-x-12 animate-shimmer"></div>
      <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-l from-transparent via-purple-300/8 to-transparent transform skew-x-6 animate-shimmer-reverse"></div>
    </div>

    <!-- Strong glass reflections -->
    <div class="absolute top-0 left-0 right-0 h-12 bg-gradient-to-r from-blue-200/30 via-purple-200/20 to-indigo-200/15 rounded-t-xl blur-md"></div>
    <div class="absolute top-2 left-2 w-20 h-20 bg-gradient-to-br from-purple-200/25 to-transparent rounded-full blur-lg"></div>

    <!-- Frosted edges -->
    <div class="absolute inset-0 rounded-xl border-2 border-purple-200/15"></div>
  </div>

  <!-- Content (will be heavily blurred by glass overlay) -->
  <div class="relative z-5">
    <!-- Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-gradient-to-br from-white/20 to-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
          <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.369 4.369 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-white truncate">
            <%= title %>
          </h3>
          <p class="text-sm text-gray-200 truncate">
            <%= style %>
          </p>
        </div>
      </div>

    </div>

    <!-- Controls -->

  </div>

  <!-- Loading Overlay (on top of everything) -->
  <div class="absolute inset-0 flex items-center justify-center z-20 bg-black/20 rounded-xl" data-fake-song-target="loadingOverlay">
    <div class="text-center">
      <div class="w-10 h-10 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto"></div>
    </div>
  </div>

  <!-- Play Button (hidden initially, shows after 5s) -->
  <div class="absolute inset-0 flex items-center justify-center z-20 hidden" data-fake-song-target="playButtonOverlay">
    <!-- SVG Play icon -->
    <%= link_to login_path, data: { turbo_frame: "modal_frame" } do %>
      <svg class="w-12 h-12 text-white/60 drop-shadow-lg cursor-pointer hover:text-white/80 transition-all duration-300 hover:scale-110"
           fill="currentColor" viewBox="0 0 24 24">
        <path d="M8 5v14l11-7z"/>
      </svg>
    <% end %>
  </div>
</div>

<style>
@keyframes shimmer {
  0% { transform: translateX(-100%) skewX(-12deg); }
  100% { transform: translateX(200%) skewX(-12deg); }
}

@keyframes shimmer-reverse {
  0% { transform: translateX(200%) skewX(6deg); }
  100% { transform: translateX(-100%) skewX(6deg); }
}

.animate-shimmer {
  animation: shimmer 3s ease-in-out infinite;
}

.animate-shimmer-reverse {
  animation: shimmer-reverse 3s ease-in-out infinite;
}
</style>
