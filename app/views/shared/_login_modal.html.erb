<%# app/views/shared/_login_modal.html.erb %>
<%#locals: (form:) %>
<!-- Main modal -->
<div id="authentication-modal" data-controller="modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed inset-0 z-50 justify-center items-center w-full bg-gray-900/50 backdrop-blur-sm">
    <div class="relative p-4 w-full max-w-md max-h-full">
        <!-- Modal content -->
        <div class="relative bg-gray-700 rounded-lg shadow-xl">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 rounded-t border-gray-600">
                <h3 class="text-xl font-semibold text-white">
                    <%= t('sessions.new.login_title') %>
                </h3>
                <button type="button" class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-600 hover:text-white rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center cursor-pointer" data-action="click->modal#close">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="p-4 md:p-5">
                <div class="space-y-4">
                    <%# Google Sign-In Button %>
                    <div>
                        <%= link_to google_sessions_path,
                                    class: "w-full text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center justify-center",
                                    data: { turbo: "false" } do %>
                        <svg class="w-4 h-4 me-2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <%= t('sessions.new.login_with_google') %>
                        <% end %>
                    </div>

                    <div class="flex justify-between items-center">
                        <div class="flex-grow border-t border-gray-600"></div>
                        <span class="flex-shrink mx-4 text-gray-300 text-sm font-medium"><%= t('sessions.new.or') %></span>
                        <div class="flex-grow border-t border-gray-600"></div>
                    </div>

                    <%# Email OTP Sign-In Section %>
                    <div id="auth_flash_messages" class="mb-3">
                    </div>

                    <%= turbo_frame_tag "auth_form_container" do %>
                        <%= render partial: "sessions/email_form", locals: { form: form } %>
                    <% end %>
                </div>
            </div>
        </div>
    </div>
</div>
