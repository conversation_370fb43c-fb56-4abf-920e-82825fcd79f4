<%# Locals: show_text (default: "Show"), hide_text (default: "Hide"), button_class (optional) %>
<% show = local_assigns.fetch(:show_text, "Show") %>
<% hide = local_assigns.fetch(:hide_text, "Hide") %>
<% btn_class = local_assigns.fetch(:button_class, "text-sm text-blue-500 hover:text-blue-400 cursor-pointer") %>

<div data-controller="collapsible"
     data-collapsible-show-text-value="<%= show %>"
     data-collapsible-hide-text-value="<%= hide %>"
     class="space-y-2">
  <button type="button"
          data-action="click->collapsible#toggle"
          data-collapsible-target="toggleButton"
          class="<%= btn_class %>"><%= show %></button>

  <div data-collapsible-target="content" class="hidden space-y-4">
    <%= yield %>
  </div>
</div>
