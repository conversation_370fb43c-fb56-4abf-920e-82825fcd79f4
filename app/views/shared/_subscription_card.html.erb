<div id="subscriptionCard" data-controller="subscription-card" data-subscription-card-target="card" data-action="click->subscription-card#visitPlans" data-subscription-card-pricing-url-value="<%= pricing_index_path(locale: I18n.locale) %>" class="p-4 shadow-lg bg-gradient-to-br from-purple-600 to-blue-500 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-white cursor-pointer mb-3">
  <div class="flex justify-between items-center mb-1">
    <h2 class="text-lg font-semibold"><%= t('.title') %></h2>
    <button data-subscription-card-target="closeButton" data-action="click->subscription-card#close" type="button" class="text-white focus:outline-none">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </div>
  <div class="flex justify-between items-end">
    <ul class="space-y-0 text-sm">
      <li class="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2 text-green-400">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <%= t('.priority_queue') %>
      </li>
      <li class="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2 text-green-400">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <%= t('.running_jobs') %>
      </li>
    </ul>
    <div>
      <%= link_to t('.subscribe'), pricing_index_path(locale: I18n.locale), data: { turbo_frame: "_top" }, class: "text-white bg-gradient-to-r from-cyan-500 to-blue-500 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-cyan-300 dark:focus:ring-cyan-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center" %>
    </div>
  </div>
</div>
