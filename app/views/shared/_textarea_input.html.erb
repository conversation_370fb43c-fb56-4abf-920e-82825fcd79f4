<%# app/views/shared/_textarea_input.html.erb %>
<%
  # Set default values for local variables if they are not provided
  label_text ||= "Your Message"
  input_id ||= "message"
  input_name ||= "message"
  rows_count ||= 4
  placeholder_text ||= "Write your thoughts here..."
  value_text ||= "" # Allows pre-filling the textarea
  custom_label_classes ||= "block mb-2 text-sm font-medium text-slate-300"
  # Default classes for the textarea itself
  default_textarea_classes = "block p-2.5 pb-12 w-full text-sm text-slate-200 bg-slate-700 rounded-lg border border-slate-600 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400"
  character_limit ||= 0 # Default to 0 if not provided

  # --- Build base options for text_area_tag ---
  options = {
    id: input_id,
    rows: rows_count,
    class: default_textarea_classes,
    placeholder: placeholder_text
  }
  options[:maxlength] = character_limit if character_limit.positive?

  # Attributes coming from the caller (e.g. `textarea_html_attributes` local)
  caller_attrs = local_assigns[:textarea_html_attributes] || {}

  # Deep-merge attributes while treating :class and :data specially
  options.deep_merge!(caller_attrs) do |key, base_val, extra_val|
    case key
    when :class then [base_val, extra_val].join(" ").strip
    when :data  then (base_val || {}).merge(extra_val)
    else             extra_val
    end
  end

  # Attach textarea-char-count controller hooks
  options[:data] ||= {}
  existing_action = options[:data][:action]
  new_action = "input->textarea-char-count#updateCount"
  options[:data][:action] = [existing_action, new_action].compact.join(" ")
  options[:data]['textarea-char-count-target'] ||= 'input'
  # --- End of options preparation ---
%>


<div class="relative"
     data-controller="textarea-char-count"
     data-textarea-char-count-limit-value="<%= character_limit %>"
>
  <label for="<%= input_id %>" class="<%= custom_label_classes %>"><%= label_text %></label>
  <%= text_area_tag input_name, value_text, options %>

  <% if character_limit > 0 %>
    <div class="absolute bottom-2 right-4 text-xs text-slate-400" data-textarea-char-count-target="count">
      0/<%= character_limit %>
    </div>
  <% end %>

  <% if local_assigns[:button_text].present? %>
    <%
      # Determine the action based on button text
      action = case local_assigns[:button_identifier]
               when 'surprise_me', 'generate_lyrics'
                 'click->openai-stream#generate'
               else
                 local_assigns[:button_action]
               end
    %>
    <button type="button"
            data-action="<%= action %>"
            data-openai-stream-type-param="<%= local_assigns[:button_identifier] %>"
            class="absolute bottom-2 left-2 text-white bg-gradient-to-br from-green-400 to-blue-600 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-green-200 dark:focus:ring-green-800 font-medium rounded-lg text-sm px-3 py-1.5 cursor-pointer">
      <%= local_assigns[:button_text] %>
    </button>
  <% end %>
</div>
