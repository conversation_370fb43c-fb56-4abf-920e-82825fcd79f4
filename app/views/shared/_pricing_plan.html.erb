<div class="w-full max-w-sm p-4 bg-slate-800 border border-slate-600 rounded-lg shadow-sm sm:p-8 flex flex-col h-full"
     data-plan-upgrade-target="planCard"
     data-plan-type="<%= plan.plan_type %>"
     data-billing-interval="<%= plan.billing_interval %>"
     data-tier="<%= Plan.plan_types[plan.plan_type] %>"
     data-text-choose-plan="<%= t('.choose_plan') %>"
     data-text-current-plan="<%= t('.current_plan') %>"
     data-text-upgrade="<%= t('.upgrade') %>"
     data-tooltip-already-subscribed="<%= t('.tooltip_already_subscribed') %>"
     data-tooltip-cant-select="<%= t('.tooltip_cant_select') %>">
<h5 class="mb-4 text-xl font-medium text-slate-300"><%= plan_name %></h5>
<div class="flex items-baseline text-white">
<span class="text-3xl font-semibold">$</span>
<span class="text-5xl font-extrabold tracking-tight"><%= price %></span>
<span class="ms-1 text-xl font-normal text-slate-400">/<%= t("plans.billing_intervals.#{billing_interval}") %></span>
</div>
<ul role="list" class="space-y-5 my-7 flex-grow">
  <%# The features variable is a JSON string. Parse it and render features. %>
  <% JSON.parse(features).each do |feature| %>
    <li class="flex items-start">
      <svg class="shrink-0 w-4 h-4 <%= feature['included'] ? 'text-blue-400' : 'text-slate-500' %> mt-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
      </svg>
      <div class="flex flex-col ms-3">
        <span class="text-base font-normal leading-tight text-slate-300 <%= 'line-through decoration-slate-500' unless feature['included'] %>">
          <% if feature['name'] == 'plans.features.credits_per_month' && plan.plan_limit && plan.plan_limit > 0 %>
            <%= t(feature['name'], value: number_with_delimiter(plan.plan_limit)) %>
          <% elsif feature['name'] == 'plans.features.concurrent_jobs' %>
            <%
              jobs_count = case plan.plan_type
                           when 'basic' then 1
                           when 'pro' then 3
                           when 'unlimited' then 5
                           else feature['value']
                           end
            %>
            <%= t(feature['name'], value: jobs_count) %>
          <% else %>
            <%= t(feature['name'], value: feature['value']) %>
          <% end %>
        </span>
        <% if !feature['included'] && billing_interval == 'month' && feature['subtext'] %>
          <span class="text-xs text-slate-500"><%= feature['subtext'] %></span>
        <% end %>
      </div>
    </li>
  <% end %>
</ul>
<div class="mt-auto">
  <%= form_with url: orders_path, method: :post, data: { turbo: false } do |form| %>
    <%= form.hidden_field :plan_id, value: plan.id %>
    <%
      button_text_key = '.choose_plan'
      if current_user.plan.same_as?(plan)
        button_text_key = '.current_plan'
      elsif plan.upgrade_from?(current_user.plan)
        button_text_key = '.upgrade'
      end
    %>
    <%= form.submit t(button_text_key), class: "text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-900 font-medium rounded-lg text-sm px-5 py-2.5 inline-flex justify-center w-full text-center cursor-pointer", disabled: current_user.plan.same_as?(plan) %>
  <% end %>
</div>
</div>
