<%# app/views/shared/_generation_task_card.html.erb %>
<%# Generation task card showing progress and completion status %>
<% item_data = task.response_data&.dig("data", "data", index) %>
<% streaming_audio_url = (task.text_received? || task.first_data_received?) ? task.streaming_audio_urls[index] : nil %>

<turbo-frame id="generation_task_<%= task.id %>_<%= index %>" class="block">
  <div class="bg-slate-700 border border-slate-600 rounded-lg shadow-md p-3 flex items-center justify-between hover:border-slate-500 transition-colors duration-150 cursor-pointer"
       <% if streaming_audio_url.present? %>
         data-controller="song-card"
         data-song-card-audio-url-value="<%= streaming_audio_url %>"
         data-song-card-song-title-value="<%= item_data&.dig('title') || task.title || t('.generating') %>"
         data-song-card-image-url-value="<%= item_data&.dig('image_url') || asset_path('default_album_art_small.png') %>"
         data-song-card-ai-generated-value="<%= t('songs.ai_generated') %>"
       <% end %>>

    <div class="flex items-center flex-grow min-w-0">
      <%# Song image or loading spinner %>
      <div class="relative w-16 h-16 bg-slate-600 rounded mr-4 flex-shrink-0 flex items-center justify-center group">
        <% if item_data&.dig("image_url").present? %>
          <%= image_tag(item_data["image_url"],
                        alt: item_data["title"] || t('.generated_song'),
                        class: "w-full h-full object-cover rounded") %>
        <% else %>
            <svg class="w-8 h-8 text-slate-400 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"/>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
            </svg>
        <% end %>

        <%# Play button overlay for streaming audio %>
        <% if streaming_audio_url.present? %>
          <button class="cursor-pointer absolute inset-0 flex items-center justify-center bg-black/30 rounded streaming-play-button"
                  data-action="click->song-card#playAudio"
                  data-song-card-target="playButton">
            <svg class="w-10 h-10 lg:w-8 lg:h-8 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        <% end %>
      </div>

      <%# Song details %>
      <div class="min-w-0 flex-1">
        <h3 class="text-lg lg:text-base font-semibold text-white truncate mb-1" title="<%= item_data&.dig("title") || task.title || t('.generating') %>">
          <%= item_data&.dig("title") || task.title || t('.generating') %>
        </h3>
        <p class="text-xs text-slate-400">
          <% if task.completed? %>
            <%= t('.generated_successfully') %>
          <% elsif task.text_received? %>
            <%= t('.streaming_ready') %>
          <% elsif task.first_data_received? %>
            <%= t('.processing_audio') %>
          <% else %>
            <%= t('.generation_in_progress') %>
          <% end %>
        </p>
      </div>
    </div>

    <%# Status indicator %>
    <div class="flex-shrink-0 ml-4 lg:ml-2 p-2">
      <% if task.failed? %>
        <svg class="w-6 h-6 text-red-500" fill="currentColor" viewBox="0 0 24 24">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
      <% elsif streaming_audio_url.present? %>
        <svg class="w-6 h-6 text-orange-500" fill="currentColor" viewBox="0 0 24 24">
          <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v6.114A4.369 4.369 0 005 11c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.369 4.369 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
        </svg>
      <% else %>
        <svg class="w-6 h-6 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"/>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
          </svg>
      <% end %>
    </div>
  </div>
</turbo-frame>
