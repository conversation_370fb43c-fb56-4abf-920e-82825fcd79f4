<%# app/views/shared/_song_card.html.erb %>
<% if song&.id %>
  <%= link_to song_path(song),
      data: { turbo_frame: "song_detail_area", action: "click->song-card#selectSong" },
      class: "block" do %>

    <div class="bg-slate-700 border border-slate-600 rounded-lg shadow-md p-3 flex items-center justify-between hover:border-slate-500 transition-all duration-150 cursor-pointer"
         data-controller="song-card"
         data-song-card-id-value="<%= song.id %>"
         data-song-card-audio-url-value="<%= song.audio_url %>"
         data-song-card-song-title-value="<%= song.title || t('.untitled_song') %>"
         data-song-card-image-url-value="<%= song.image_url || asset_path('default_album_art_small.png') %>"
         data-song-card-ai-generated-value="<%= t('songs.ai_generated') %>"
         data-song-card-target="card">

      <div class="flex items-center flex-grow min-w-0">
        <%# Song image with play button %>
        <div class="relative w-16 h-16 bg-slate-500 rounded mr-4 flex-shrink-0">
          <%= image_tag(song.image_url.presence || 'default_album_art_small.png',
                        alt: song.title,
                        class: "w-full h-full object-cover rounded") %>

          <%# Play button overlay %>
          <button class="cursor-pointer absolute inset-0 flex items-center justify-center bg-black/30 rounded"
                  data-action="click->song-card#playAudio"
                  data-song-card-target="playButton"
                  onclick="event.preventDefault(); event.stopPropagation();">
            <%# Play icon %>
            <svg class="w-10 h-10 lg:w-6 lg:h-6 text-white drop-shadow-lg"
                 fill="currentColor" viewBox="0 0 24 24"
                 data-song-card-target="playIcon">
              <path d="M8 5v14l11-7z"/>
            </svg>
            <%# Pause icon %>
            <svg class="w-10 h-10 lg:w-6 lg:h-6 text-white drop-shadow-lg hidden"
                 fill="currentColor" viewBox="0 0 24 24"
                 data-song-card-target="pauseIcon">
              <path d="M6 4h4v16H6zm8 0h4v16h-4z"/>
            </svg>
          </button>
        </div>

        <%# Song details %>
        <div class="min-w-0 flex-1">
          <h3 class="text-md lg:text-base font-semibold text-white truncate" title="<%= song.title %>">
            <%= song.title || t('.untitled_song') %>
          </h3>

          <% if song.style.present? %>
            <span class="text-sm lg:text-xs font-medium inline-block py-1 px-2 lg:py-0.5 lg:px-2 mb-2 lg:mb-1 uppercase rounded-full text-blue-300 bg-blue-900/30 border border-blue-700/50">
                <%= song.style.split(',').first.strip %>
              </span>
          <% end %>

          <p class="text-sm lg:text-xs text-slate-400">
            <% if song.duration.present? %>
              <%= "#{song.duration.to_i / 60}:#{(song.duration.to_i % 60).to_s.rjust(2, '0')}" %>
            <% else %>
              0:00
            <% end %>
          </p>
        </div>
      </div>

      <%# Like button %>
      <div class="flex-shrink-0 ml-4 lg:ml-2">
        <turbo-frame id="<%= dom_id(song, :like) %>">
          <%= render 'songs/like_button', song: song, variant: :list %>
        </turbo-frame>
      </div>
    </div>
  <% end %>
<% else %>
  <% Rails.logger.warn "Attempted to render song card with invalid song: #{song.inspect}" %>
<% end %>
