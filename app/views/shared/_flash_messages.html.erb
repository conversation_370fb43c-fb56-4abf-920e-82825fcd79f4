<div id="toast-container" class="fixed top-16 left-1/2 -translate-x-1/2 max-w-screen-xl z-50 px-4 w-full sm:w-auto">
  <%
    # Determine if locals were passed explicitly (e.g., from model broadcasting)
    explicit_type      = local_assigns[:flash_type]
    explicit_messages  = local_assigns[:flash_messages]

    # Figure out whether we have something to show
    has_flash = if explicit_messages.present?
                   true
                 else
                   flash.any?
                 end

    # Resolve type (success / error)
    flash_type = if explicit_type.present?
                   explicit_type
                 elsif has_flash
                   first_flash_key = flash.keys.first.to_sym
                   [:alert, :error, :danger].include?(first_flash_key) ? "error" : "success"
                 else
                   "success"
                 end

    # Resolve message html
    flash_messages = if explicit_messages.present?
                        explicit_messages.to_s.html_safe
                      elsif has_flash
                        flash.map { |type, message| message }.join("<br>").html_safe
                      else
                        ""
                      end
  %>

  <%# Toast controller contains two templates and renders one of them based on initial flash %>
  <div
    data-controller="toast"
    data-toast-type-value="<%= flash_type %>"
    data-toast-message-value="<%= flash_messages %>" <%# Pass messages directly %>
    data-toast-duration-value="5"
    data-toast-auto-close-value="true"
    id="toast-element"
    class="<%= has_flash ? '' : 'opacity-0 pointer-events-none' %> transition-opacity" <%# Initially visible if has_flash %>
    role="alert">

    <template data-toast-target="successTemplate">
      <%= render partial: "shared/flash_message_success", locals: { flash_messages: "" } %>
    </template>

    <template data-toast-target="errorTemplate">
      <%= render partial: "shared/flash_message_error", locals: { flash_messages: "" } %>
    </template>

    <div data-toast-target="contentContainer">
      <% if has_flash %>
        <%= render partial: "shared/flash_message_#{flash_type}", locals: { flash_messages: flash_messages } %>
      <% end %>
    </div>
  </div>
</div>
