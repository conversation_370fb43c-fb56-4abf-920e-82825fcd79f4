<%# app/views/shared/_global_audio_player.html.erb %>
<div id="library-audio-player-container"
     class="audio-player-container p-4 rounded-xl w-full hidden"
     data-controller="audio-player"
     data-audio-player-mode-value="global"
     data-audio-player-debug-value="<%= Rails.env.development? %>">

  <div class="w-full">
    <div class="flex items-center gap-4">
      <!-- Song Info Section -->
      <div class="song-info">
        <div class="album-art">
          <img class="hidden w-full h-full object-cover rounded-lg"
               data-audio-player-target="albumImage"
               src=""
               alt="Album art">
          <svg class="default-icon"
               data-audio-player-target="defaultIcon"
               xmlns="http://www.w3.org/2000/svg"
               fill="none"
               viewBox="0 0 24 24"
               stroke="currentColor">
            <path stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM21 16c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2z" />
          </svg>
        </div>

        <div class="song-details">
          <div class="song-title" data-audio-player-target="songTitle">
            <%= t('.no_song_selected') %>
          </div>
          <div class="song-artist" data-audio-player-target="songArtist">
            <%= t('.select_song_to_play') %>
          </div>
        </div>
      </div>

      <!-- Custom Controls -->
      <div class="custom-controls flex-1">
        <!-- Progress Section -->
        <div class="progress-section">
          <div class="progress-bar-container"
               data-audio-player-target="progressContainer"
               data-action="click->audio-player#seekTo">
            <div class="progress-bar"
                 data-audio-player-target="progressBar"
                 style="width: 0%"></div>
          </div>

          <div class="time-info">
            <span data-audio-player-target="currentTime">0:00</span>
            <span data-audio-player-target="duration">0:00</span>
          </div>
        </div>

        <!-- Playback Controls -->
        <div class="flex items-center justify-center gap-4 mt-2">
          <!-- Skip Backward Button -->
          <button class="control-button"
                  data-action="click->audio-player#skipBackward"
                  title="Skip backward" aria-label="Skip backward">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0019 16V8a1 1 0 00-1.6-.8l-5.333 4zM4.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0011 16V8a1 1 0 00-1.6-.8l-5.334 4z" />
            </svg>
          </button>

          <!-- Play/Pause Button -->
          <button class="play-button"
                  data-audio-player-target="playButton"
                  data-action="click->audio-player#playPause">
            <svg class="play-icon"
                 data-audio-player-target="playIcon"
                 xmlns="http://www.w3.org/2000/svg"
                 fill="currentColor"
                 viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
            <svg class="pause-icon hidden"
                 data-audio-player-target="pauseIcon"
                 xmlns="http://www.w3.org/2000/svg"
                 fill="currentColor"
                 viewBox="0 0 24 24">
              <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
            </svg>
          </button>

          <!-- Skip Forward Button -->
          <button class="control-button"
                  data-action="click->audio-player#skipForward"
                  title="Skip forward" aria-label="Skip forward">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.933 12.8a1 1 0 000-1.6L6.6 7.2A1 1 0 005 8v8a1 1 0 001.6.8l5.333-4zM19.933 12.8a1 1 0 000-1.6l-5.333-4A1 1 0 0013 8v8a1 1 0 001.6.8l5.333-4z" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Volume Control -->
      <div class="volume-control">
        <button class="volume-button"
                data-audio-player-target="volumeButton"
                data-action="click->audio-player#mute">
          <svg class="volume-icon"
               xmlns="http://www.w3.org/2000/svg"
               fill="none"
               viewBox="0 0 24 24"
               stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
          </svg>
        </button>

        <div class="volume-slider"
             data-audio-player-target="volumeSlider"
             data-action="click->audio-player#adjustVolume">
          <div class="volume-fill"
               data-audio-player-target="volumeFill"
               style="width: 100%"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error Message -->
  <div class="audio-error-message"
       data-audio-player-target="errorMessage"></div>

  <!-- Hidden audio element -->
  <audio class="custom-audio-element"
         data-audio-player-target="audio"
         preload="metadata"
         crossorigin="anonymous"
         playsinline
         webkit-playsinline
         x-webkit-airplay="allow">
    <p><%= t('.no_browser_support') %></p>
  </audio>
</div>
