<%# SOLUTION 2: Use CSS contain to control stacking contexts %>
<div class="relative h-full w-full">
  <div class="relative grid grid-cols-1 lg:grid-cols-3 gap-4 h-full">
    <%# Song Library (2/3 of workspace) %>
    <div class="lg:col-span-2 lg:overflow-y-auto h-full">
      <turbo-frame id="song_library_frame" class="h-full">
        <%= render 'songs/song_library', songs: songs, tasks_in_progress: tasks_in_progress %>
      </turbo-frame>
    </div>

    <%# Song Detail (1/3 of workspace) %>
    <div class="relative lg:col-span-1 lg:overflow-y-auto h-full">
      <% if current_user %>
        <%= turbo_stream_from [current_user, 'song_detail'] %>
      <% end %>
      <%= turbo_frame_tag "song_detail_area", class: "h-full" do %>
        <%= render 'songs/song_detail', song: first_song, show_create_first: show_create_first %>
      <% end %>
    </div>
  </div>

  <%# Player positioned with mix-blend-mode to force compositing %>
  <div class="absolute bottom-0 right-0 z-50 w-full">
    <%= render 'shared/global_audio_player' %>
  </div>
</div>

<template data-fake-song-target="fakeCardTemplate">
  <%= render 'shared/fake_song_card', title: "Generating song...", style: "Please wait a moment" %>
  <%= render 'shared/fake_song_card', title: "Creating melody...", style: "AI is composing" %>
</template>

<template data-fake-song-target="loginPromptCardTemplate">
  <%= render 'shared/fake_login_prompt_card' %>
</template>
