<%# Locals: label_text, input_name_prefix, tags (array of hashes), html_id_context (optional), wrapper_data_attributes (optional hash) %>
<% input_name = local_assigns.fetch(:input_name_prefix, label_text.parameterize) %>
<% id_context_base = local_assigns.fetch(:html_id_context, input_name) %>

<div class="mb-4"
     data-controller="selectable-tag-group"
     <%= tag.attributes(local_assigns.fetch(:wrapper_data_attributes, {})) %>>
  <label class="block mb-1 text-sm font-medium text-slate-300"><%= label_text %></label>
  <div class="flex flex-wrap gap-2" data-selectable-tag-group-target="tagsContainer">
    <% tags.each_with_index do |tag_data, index| %>
      <%
        base_button_classes = "inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900 focus:ring-blue-500 transition-colors duration-150 cursor-pointer"
        input_id_for_tag = "#{id_context_base.to_s.parameterize}-#{tag_data[:value].to_s.parameterize}-#{index}"

        # Combine the component's own select action with any extra action passed in (e.g., from lyrics_options_controller)
        action_base = "selectable-tag-group#select"
        extra_action = tag_data[:data_action]
        combined_action = [action_base, extra_action].compact.join(" ")

        # Isolate any other data attributes meant for the input element itself
        input_data_attributes = tag_data.select { |k, _| k.to_s.start_with?('data-') && k != :data_action }
      %>

      <label for="<%= input_id_for_tag %>"
             class="<%= base_button_classes %>"
             data-tag-value="<%= tag_data[:value] %>">
        <%= tag_data[:display_text] %>
        <% if tag_data[:is_premium] %>
          <%= image_tag "/premium_icon.svg", class: "ms-1.5 h-4 w-4" %>
        <% end %>
      </label>

      <input type="radio"
             id="<%= input_id_for_tag %>"
             name="<%= input_name %>"
             value="<%= tag_data[:value] %>"
             class="sr-only"
             <%= 'checked' if tag_data[:is_selected_by_default] %>
             data-default="<%= tag_data[:is_selected_by_default] ? 'true' : 'false' %>"
             data-action="<%= combined_action %>"
             data-selectable-tag-group-target="hiddenInput"
             <%= tag.attributes(input_data_attributes) %>
             >
    <% end %>
  </div>
</div>
