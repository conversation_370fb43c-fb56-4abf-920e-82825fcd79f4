<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Ai-Music.Me" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1,viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>
    <link rel="preload" href="/icon.svg" as="image" type="image/svg+xml">

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-slate-900 text-white">
    <%= turbo_frame_tag "modal_frame" %>
    <%= render "shared/flash_messages" %>
    <%# Subscribe the signed-in user to flash message broadcasts so we can push toasts from background jobs %>
    <%= turbo_stream_from current_user, :flash_messages if defined?(current_user) && current_user.present? %>

    <!-- Mobile Menu Button -->
    <button data-drawer-target="logo-sidebar"
            data-drawer-toggle="logo-sidebar"
            aria-controls="logo-sidebar"
            type="button"
            class="fixed z-50 inline-flex items-center p-2 text-sm text-gray-400 rounded-lg sm:hidden hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-600 bg-gray-800 shadow-lg"
            style="top: calc(1rem + env(safe-area-inset-top, 0px)); left: calc(1rem + env(safe-area-inset-left, 0px));">
      <span class="sr-only"><%= t('layouts.sidebar.open_sidebar') %></span>
      <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
        <path clip-rule="evenodd" fill-rule="evenodd" d="M2 4.75A.75.75 0 012.75 4h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 4.75zm0 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM2 10a.75.75 0 01.75-.75h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 10z"></path>
      </svg>
    </button>

    <!-- Sidebar -->
    <aside id="logo-sidebar"
           data-drawer-backdrop="false"
           class="fixed top-0 left-0 z-40 w-56 h-screen transition-transform -translate-x-full sm:translate-x-0"
           aria-label="Sidebar"
           aria-hidden="true"
           style="padding-top: env(safe-area-inset-top); padding-bottom: env(safe-area-inset-bottom);">
      <div class="h-full px-3 flex flex-col bg-gray-800"
           style="padding-top: calc(1rem + env(safe-area-inset-top, 0px)); padding-bottom: calc(1rem + env(safe-area-inset-bottom, 0px));">
        <!-- Header with Close Button for Mobile -->
        <div class="flex-shrink-0">
          <div class="flex items-center justify-between ps-2.5 mb-5 mt-4">
            <a href="<%= root_path %>" class="flex items-center group">
              <span class="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">AI-Music.Me</span>
            </a>
            <!-- Close button for mobile -->
            <button data-drawer-hide="logo-sidebar"
                    type="button"
                    class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg sm:hidden">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Navigation -->
        <div class="flex-1 overflow-y-auto thin-scrollbar">
          <ul class="font-medium">
            <li>
              <a href="<%= root_path %>" class="flex items-center p-2 text-base font-normal text-white rounded-lg hover:bg-slate-700 group transition duration-75 <%= sidebar_active_class(root_path) %>">
                <svg class="w-6 h-6 text-gray-400 transition duration-75 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"/>
                </svg>
                <span class="ml-3"><%= t('layouts.sidebar.create_music') %></span>
              </a>
            </li>
          </ul>

          <!-- Upgrade Section -->
          <% if current_user %>
            <div id="sidebar-upgrade-section" class="mt-2">
              <%= render "layouts/sidebar_upgrade_items" %>
            </div>
          <% end %>
        </div>

        <!-- Bottom Section -->
        <div id="sidebar-plan-card-section" class="flex-shrink-0 space-y-3">
          <%= render 'shared/plan_card' %>
          <div id="sidebar-user-auth-section">
            <%= render 'layouts/sidebar_user_auth' %>
          </div>
        </div>
      </div>
    </aside>

    <!-- Main Content -->
    <div class="fixed top-0 right-0 bottom-0 left-0 sm:left-56 flex flex-col"
         style="padding-top: env(safe-area-inset-top); padding-bottom: env(safe-area-inset-bottom); padding-left: env(safe-area-inset-left); padding-right: env(safe-area-inset-right);">
      <div class="flex-1 p-4 pt-16 sm:pt-4 min-h-0 overflow-y-auto pb-[var(--audio-h)] main-scrollable">
        <%= yield %>
      </div>
    </div>
  </body>
</html>
