<!-- Language Selector Dropdown -->
<div class="relative" data-controller="dropdown">
  <button class="flex items-center justify-between w-full p-4 bg-gradient-to-r from-indigo-500/20 to-purple-600/20 hover:from-indigo-500/30 hover:to-purple-600/30 text-white/90 hover:text-white rounded-xl border border-white/10 shadow-lg hover:shadow-indigo-500/20 cursor-pointer"
          data-action="click->dropdown#toggle"
          data-dropdown-target="button">
        <div class="flex items-center text-white/90">
      <div class="w-10 h-10 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-full flex items-center justify-center mr-3 shadow-md">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"/>
        </svg>
      </div>
      <div class="min-w-0 flex-1">
        <span class="text-sm font-semibold truncate block text-white">
          <%= case I18n.locale.to_s
              when 'en' then 'English'
              when 'fr' then 'Français'
              when 'zh' then '中文'
              when 'zh-TW' then '繁體中文'
              when 'es' then 'Español'
              when 'de' then 'Deutsch'
              when 'it' then 'Italiano'
              when 'ja' then '日本語'
              when 'ko' then '한국어'
              when 'id' then 'Bahasa Indonesia'
              when 'pt' then 'Português'
              when 'vi' then 'Tiếng Việt'
              else 'English'
              end %>
        </span>
      </div>
    </div>
    <svg class="w-4 h-4 text-white/60 transition-transform duration-200"
         data-dropdown-target="chevron"
         fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
    </svg>
  </button>

  <!-- Dropdown Menu -->
  <div class="absolute bottom-full left-0 right-0 mb-2 bg-slate-800/95 backdrop-blur-sm border border-white/10 rounded-xl shadow-xl overflow-hidden opacity-0 invisible z-50"
       data-dropdown-target="menu">

    <% [
         { code: 'en', name: 'English'},
         { code: 'fr', name: 'Français'},
         { code: 'zh', name: '中文'},
         { code: 'zh-TW', name: '繁體中文'},
         { code: 'es', name: 'Español'},
         { code: 'de', name: 'Deutsch'},
         { code: 'it', name: 'Italiano'},
         { code: 'ja', name: '日本語'},
         { code: 'ko', name: '한국어'},
         { code: 'id', name: 'Bahasa Indonesia'},
         { code: 'pt', name: 'Português'},
         { code: 'vi', name: 'Tiếng Việt'},
       ].each do |language| %>
      <%= link_to url_for(locale: language[:code]),
          class: "flex items-center px-4 py-3 text-white/90 hover:text-white hover:bg-gradient-to-r hover:from-indigo-500/20 hover:to-purple-500/20 #{'bg-gradient-to-r from-indigo-500/10 to-purple-500/10' if I18n.locale.to_s == language[:code]}" do %>
        <div class="flex-1">
          <span class="text-sm font-medium"><%= language[:name] %></span>
        </div>
        <% if I18n.locale.to_s == language[:code] %>
          <svg class="w-4 h-4 text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
        <% end %>
      <% end %>
    <% end %>
  </div>
</div>
