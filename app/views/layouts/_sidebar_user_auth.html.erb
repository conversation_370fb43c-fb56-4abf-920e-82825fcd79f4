<!-- User Authentication Section -->
<% if current_user %>
  <div class="space-y-2">

    <!-- Language Selector -->
    <%= render 'layouts/sidebar_language_selector' %>

    <!-- User Greeting Card with Dropdown -->
    <div class="relative" data-controller="dropdown">
      <button class="flex items-center justify-between w-full p-4 bg-gradient-to-r from-green-400/20 to-blue-600/20 hover:from-green-400/30 hover:to-blue-600/30 text-white/90 hover:text-white rounded-xl border border-white/10 shadow-lg hover:shadow-green-500/20 cursor-pointer"
              data-action="click->dropdown#toggle"
              data-dropdown-target="button">
        <div class="flex items-center text-white/90">
          <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center mr-3 shadow-md">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
            </svg>
          </div>
          <div class="min-w-0 flex-1">
            <span class="text-sm font-semibold truncate inline-block text-white max-w-[100px]">
              <%= (current_user.username || current_user.email.split('@').first) %>
            </span>
            <div class="flex items-center mt-1">
              <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
              <span class="text-xs text-white/80 capitalize font-medium">
                <%= t("shared.plan_card.#{current_user.plan_type}_plan") %>
              </span>
            </div>
          </div>
        </div>
        <svg class="w-4 h-4 text-white/60 transition-transform duration-200"
             data-dropdown-target="chevron"
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
        </svg>
      </button>

      <!-- Dropdown Menu -->
      <div class="absolute bottom-full left-0 right-0 mb-2 bg-slate-800/95 backdrop-blur-sm border border-white/10 rounded-xl shadow-xl overflow-hidden opacity-0 invisible z-50"
           data-dropdown-target="menu">
           <!-- Support Option -->
           <button type="button"
                   data-controller="clipboard"
                   data-clipboard-text-value="<EMAIL>"
                   data-clipboard-message-value="<%= t('layouts.sidebar.email_copied', email: '<EMAIL>') %>"
                   data-action="clipboard#copy"
                   class="flex items-center w-full px-4 py-3 text-white/90 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20 cursor-pointer">
             <svg class="w-4 h-4 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"/>
             </svg>
             <span class="text-sm font-medium"><%= t('layouts.sidebar.support') %></span>
           </button>

        <!-- Logout Option -->
        <%= link_to logout_path,
            data: { turbo_method: :delete },
            class: "flex items-center px-4 py-3 text-white/90 hover:text-white hover:bg-gradient-to-r hover:from-red-500/20 hover:to-pink-500/20 cursor-pointer" do %>
          <svg class="w-4 h-4 mr-3 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
          </svg>
          <span class="text-sm font-medium"><%= t('layouts.sidebar.logout') %></span>
        <% end %>

      </div>
    </div>
  </div>
<% else %>
  <div class="space-y-2">
    <%= render 'layouts/sidebar_language_selector' %>
    <!-- Login Button -->
    <%= link_to login_path,
                data: { turbo_frame: "modal_frame" },
                class: "flex items-center justify-center p-3 bg-gradient-to-r from-green-500/20 to-blue-600/20 hover:from-green-500/30 hover:to-blue-600/30 text-white/90 hover:text-white rounded-xl border border-white/10 shadow-lg hover:shadow-green-500/20 cursor-pointer w-full" do %>
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1"/>
      </svg>
      <span class="text-sm font-semibold"><%= t('layouts.sidebar.login') %></span>
    <% end %>
  </div>
<% end %>
