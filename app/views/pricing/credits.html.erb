<div class="container mx-auto px-4 py-8">
  <h1 class="text-3xl font-bold text-center mb-6 text-white"><%= t('.title') %></h1>
  <p class="text-center text-slate-400 mb-8"><%= t('.subtitle') %></p>

  <div class="max-w-lg mx-auto lg:max-w-xl">
    <h3 class="mb-6 text-xl font-semibold text-white text-center"><%= t('.packages_title') %></h3>

    <%= form_with url: orders_path, method: :post, local: true, data: { controller: "credit-selection", turbo_confirm: t('.payment_confirmation'), turbo: "false" } do |form| %>
            <div class="space-y-4 mb-8" data-credit-selection-target="packagesContainer">
        <% @credit_packages.each_with_index do |package, index| %>
          <div class="bg-slate-800 border border-slate-600 rounded-xl p-4 hover:bg-purple-900/20 transition-colors cursor-pointer"
               data-action="click->credit-selection#selectPackage"
               data-plan-id="<%= package.id %>">
            <div class="flex items-center">
              <%= form.radio_button :plan_id, package.id,
                  id: "credits-#{package.plan_limit}",
                  class: "w-5 h-5 text-blue-400 bg-slate-700 border-slate-600 focus:ring-blue-500 focus:ring-2",
                  checked: index == 1,
                  data: { credit_selection_target: "radioInput" } %>
              <label for="credits-<%= package.plan_limit %>" class="flex-1 ml-4 flex justify-between items-center cursor-pointer">
                <span class="text-lg font-medium text-white"><%= t(package.name) %></span>
                <span class="text-xl font-bold text-blue-400">$<%= package.price %></span>
              </label>
            </div>
          </div>
        <% end %>
      </div>

      <%= form.submit t('.purchase_credits'),
          class: "w-full text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:bg-gradient-to-l focus:ring-4 focus:outline-none focus:ring-purple-800 font-medium rounded-xl text-lg px-6 py-4 text-center transition-all duration-200 hover:scale-[1.02] cursor-pointer",
          data: { credit_selection_target: "submitButton" } %>
    <% end %>
  </div>

  <div class="text-center mt-8">
    <%= link_to pricing_index_path,
        class: "inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-slate-300 bg-slate-800 border border-slate-600 rounded-xl hover:bg-slate-700 hover:text-white focus:ring-4 focus:outline-none focus:ring-slate-500 transition-colors cursor-pointer" do %>
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
      </svg>
      <%= t('.upgrade_plan_instead') %>
    <% end %>
  </div>
</div>
