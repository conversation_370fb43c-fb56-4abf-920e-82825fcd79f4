<div class="container mx-auto px-4 py-8"
     data-controller="pricing-toggle plan-upgrade"
     <% if current_user&.plan %>
     data-plan-upgrade-current-plan-type-value="<%= current_user.plan_type %>"
     data-plan-upgrade-current-billing-interval-value="<%= current_user.plan.billing_interval %>"
     data-plan-upgrade-current-tier-value="<%= Plan.plan_types[current_user.plan_type] %>"
     <% else %>
     data-plan-upgrade-current-plan-type-value="free"
     data-plan-upgrade-current-billing-interval-value="month"
     data-plan-upgrade-current-tier-value="0"
     <% end %>>
  <h1 class="text-3xl font-bold text-center mb-6 text-white"><%= t('.title') %></h1>

  <div class="flex justify-center mb-8">
    <div class="relative flex w-full sm:w-1/2 bg-slate-700 rounded-full p-1 shadow-inner" role="group">
      <button type="button"
              data-pricing-toggle-target="monthlyButton"
              data-action="click->pricing-toggle#showMonthlyPlans"
              class="w-1/2 py-2 rounded-full text-sm font-medium focus:outline-none cursor-pointer transition-colors duration-200 ease-in-out flex items-center justify-center">
        <span class="whitespace-nowrap px-4"><%= t('.monthly_billing') %></span>
      </button>
      <button type="button"
              data-pricing-toggle-target="yearlyButton"
              data-action="click->pricing-toggle#showYearlyPlans"
              class="w-1/2 py-2 rounded-full text-sm font-medium focus:outline-none cursor-pointer transition-colors duration-200 ease-in-out flex flex-col items-center justify-center">
        <span class="whitespace-nowrap px-4"><%= t('.yearly_billing') %></span>
        <span class="text-green-400"><%= t('.save_20_percent') %></span>
      </button>
    </div>
  </div>

  <div data-pricing-toggle-target="monthlyPlans" class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
    <% @monthly_plans.each do |plan| %>
      <%= render partial: 'shared/pricing_plan', locals: {
        plan: plan,
        plan_name: t("plans.names.#{plan.plan_type}"),
        price: plan.price,
        features: plan.description,
        billing_interval: plan.billing_interval
      } %>
    <% end %>
  </div>

  <div data-pricing-toggle-target="yearlyPlans" class="hidden grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
    <% @yearly_plans.each do |plan| %>
      <%= render partial: 'shared/pricing_plan', locals: {
        plan: plan,
        plan_name: t("plans.names.#{plan.plan_type}"),
        price: plan.price,
        features: plan.description,
        billing_interval: plan.billing_interval
      } %>
    <% end %>
  </div>
</div>
