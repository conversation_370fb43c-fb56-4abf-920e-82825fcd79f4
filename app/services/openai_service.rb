# app/services/openai_service.rb
class OpenaiService
  include HTTParty

  base_uri "https://api.moleapi.com"

  def initialize
    @api_key = Rails.application.credentials.openai&.api_key
    raise "OpenAI API key not found in credentials" unless @api_key
  end

  # Generate surprise me content with streaming
  def generate_surprise_me_stream(locale = "en", &block)
    user_prompt = "Generate a creative and unique song description that would inspire an AI music generator. " \
                  "Include musical style, mood, instruments, and atmosphere. Keep it under 300 characters. " \
                  "Be creative and surprising - mix genres, add unexpected elements. " \
                  "Example styles: cinematic orchestral with trap beats, medieval folk meets synthwave, " \
                  "jazz fusion with heavy metal guitars, ambient electronic with live orchestra. " \
                  "Make it vivid and inspiring!" \
                  "DONT INCLUDE TITLE, ARTIST, OR ANYTHING ELSE. JUST THE DESCRIPTION."

    user_prompt += "\n\nGenerate the response in #{language_name(locale)}." if locale.present? && locale.to_s != "en"

    system_message = PromptService.system_message
    stream_chat_completion(user_prompt, system_message, &block)
  end

  # Generate lyrics with streaming
  def generate_lyrics_stream(locale = "en", &block)
    user_prompt = "You are an expert songwriter. Generate song lyrics. " \
                  "Generate lyrics with necessary label, like [Verse], [Chorus], [Bridge], etc. " \
                  "The lyrics should be emotionally resonant and memorable, with vivid imagery and strong hooks. " \
                  "Keep the total length to about 700 characters. " \
                  "MUST: Each line of the song should be a new line in the output. Use newlines to separate each line. "\
                  "MUST: DO NOT INCLUDE the title, and any other description text. RESULT SHOULD BE ONLY THE LYRICS."

    user_prompt += "\n\nGenerate the response in #{language_name(locale)}." if locale.present? && locale.to_s != "en"

    system_message = PromptService.system_message
    stream_chat_completion(user_prompt, system_message, &block)
  end

  private

  def language_name(locale)
    case locale.to_s.downcase
    when "zh"
      "Chinese"
    when "fr"
      "French"
    when "zh-tw"
      "Chinese (Traditional)"
    when "es"
      "Spanish"
    when "de"
      "German"
    when "it"
      "Italian"
    when "ja"
      "Japanese"
    when "ko"
      "Korean"
    when "id"
      "Indonesian"
    when "pt"
      "Portuguese"
    when "vi"
      "Vietnamese"
    else
      "English"
    end
  end

  def stream_chat_completion(user_prompt, system_message = nil, &block)
    headers = {
      "Authorization" => "Bearer #{@api_key}",
      "Content-Type" => "application/json",
      "Accept" => "text/event-stream"
    }

    messages = []
    messages << { role: "system", content: system_message } if system_message.present?
    messages << { role: "user", content: user_prompt }

    body = {
      model: "gpt-4o-mini",
      messages: messages,
      stream: true,
      temperature: 0.8,
      max_tokens: 1000
    }

    buffer = ""
    # Use HTTParty to make the request
    response = self.class.post("/v1/chat/completions", {
      headers: headers,
      body: body.to_json,
      timeout: 60,
      stream_body: true
    }) do |fragment|
      process_stream_chunk(fragment, &block)
    end

    # Check if there was an HTTP error
    unless response.success?
      error_msg = "HTTP #{response.code}: #{response.message}"
      Rails.logger.error "Mole API error: #{error_msg}"
      block.call(nil, true, error_msg) if block_given?
    end

  rescue Net::TimeoutError => e
    Rails.logger.error "Mole API timeout: #{e.message}"
    block.call(nil, true, "Request timed out. Please try again.") if block_given?
  rescue => e
    Rails.logger.error "Mole API error: #{e.message}"
    block.call(nil, true, e.message) if block_given?
  end

  def process_stream_chunk(chunk, &block)
    return unless block_given?

    # Split chunk into lines
    lines = chunk.split("\n")

    lines.each do |line|
      line = line.strip
      next if line.empty?

      # Skip non-data lines
      next unless line.start_with?("data: ")

      # Extract the JSON data
      data = line[6..-1] # Remove 'data: ' prefix

      # Check for stream completion
      if data == "[DONE]"
        block.call(nil, true, nil)
        return
      end

      begin
        # Parse the JSON response
        parsed = JSON.parse(data)

        # Extract content from the delta
        content = parsed.dig("choices", 0, "delta", "content")

        if content && !content.empty?
          # Replace newlines with <br> tags for proper HTML rendering.
          # This ensures that line breaks in the AI's response are
          # displayed correctly in the browser.
          html_content = content.gsub("\n", "<br>")
          # Yield the content chunk to the controller
          block.call(html_content, false, nil)
        end

      rescue JSON::ParserError => e
        # Skip malformed JSON chunks - this is normal in streaming
        Rails.logger.debug "Skipping malformed JSON in stream: #{e.message}"
        next
      end
    end
  end
end
