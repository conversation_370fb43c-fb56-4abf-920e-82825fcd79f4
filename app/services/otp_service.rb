class OtpService
  OTP_LENGTH = 6  # 6位数字验证码
  OTP_EXPIRY = 10.minutes  # 10分钟有效期
  CACHE_PREFIX = "otp:"

  # 为用户生成 OTP 并返回 JWT
  def self.generate_otp_for_user(email)
    # 生成随机 OTP
    otp = generate_otp

    # 创建 JWT payload（只包含 exp 和邮箱）
    payload = {
      sub: email,          # 用户邮箱
      exp: OTP_EXPIRY.from_now.to_i  # 过期时间
    }

    # 派生签名密钥（master key + OTP）
    signing_key = derive_key_from_otp(otp)

    # 使用派生的密钥签名 JWT
    token = JWT.encode(payload, signing_key, "HS256")

    # 返回 OTP 和 token（OTP 用于发送给用户）
    { otp: otp, token: token }
  end

  # 验证 OTP
  def self.verify_otp(token, provided_otp)
    begin
      # 派生验证密钥
      verification_key = derive_key_from_otp(provided_otp)

      # 尝试使用派生的密钥解码 JWT
      payload = JWT.decode(token, verification_key, true, { algorithm: "HS256" })[0]

      # 提取用户邮箱
      email = payload["sub"]

      # 检查是否已经被验证过（防止重复使用）
      verified_key = "#{CACHE_PREFIX}#{email}:#{provided_otp}"
      if Rails.cache.read(verified_key) == true
        return { success: false, error: "used_otp" }
      end

      # 验证成功，在缓存中标记为已验证
      Rails.cache.write(verified_key, true, expires_in: OTP_EXPIRY)

      { success: true, email: email }
    rescue JWT::ExpiredSignature
      { success: false, error: "expired_otp" }
    rescue JWT::DecodeError
      # 解码错误说明 OTP 不正确
      { success: false, error: "invalid_otp" }
    end
  end

  private

  # 生成随机 OTP
  def self.generate_otp
    "%06d" % SecureRandom.random_number(10**OTP_LENGTH)
  end

  # 从 OTP 派生密钥
  def self.derive_key_from_otp(otp)
    # 使用 master key 和 OTP 创建一个唯一的密钥
    # 这里使用 OpenSSL 的 PBKDF2 算法进行密钥派生
    OpenSSL::PKCS5.pbkdf2_hmac(
      otp,
      Rails.application.secret_key_base,
      1000,  # 迭代次数
      32,    # 生成的密钥长度
      OpenSSL::Digest::SHA256.new
    )
  end
end
