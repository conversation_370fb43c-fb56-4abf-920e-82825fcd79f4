# app/services/prompt_service.rb
class PromptService
  PROMPTS_PATH = Rails.root.join("prompt").freeze
  CACHE_KEY = "system_prompt".freeze

  # Loads, combines, and caches the content of all markdown files
  # in the prompt directory to be used as a system message.
  def self.system_message
    Rails.cache.fetch(CACHE_KEY, expires_in: 1.hour) do
      build_system_message
    end
  end

  private

  def self.build_system_message
    initial_prompt = <<~PROMPT
      You are an expert AI assistant for "AI Music", a platform that generates music from text prompts.
      Your primary role is to help users write effective prompts to create the music they want.
      You must be friendly, helpful, and encouraging.

      When users ask for help, use the following knowledge base to provide specific and actionable advice.
      Refer to concepts like 'meta-tags', 'style prompts', and song structure.

      --- K<PERSON><PERSON>LEDGE BASE START ---
    PROMPT

    markdown_content = Dir.glob(PROMPTS_PATH.join("*.md")).sort.map do |file_path|
      file_name = File.basename(file_path)
      content = File.read(file_path)
      "# File: #{file_name}\n\n#{content}"
    end.join("\n\n")

    footer = "\n--- KNOWLEDGE BASE END ---"

    "#{initial_prompt}\n#{markdown_content}#{footer}"
  end
end
