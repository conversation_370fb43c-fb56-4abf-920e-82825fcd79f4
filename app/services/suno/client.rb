require "uri"
require "net/http"
require "json"

module Suno
  class Client
    ENDPOINT = URI("https://apibox.erweima.ai/api/v1/generate").freeze

    def initialize(access_token: Rails.application.credentials.dig(:suno, :access_token))
      @access_token = access_token
    end

    # Sends a POST to Sun<PERSON>'s generate endpoint with provided payload.
    # Returns parsed JSON or hash with error.
    def generate_music(payload)
      body = payload.to_json

      http = Net::HTTP.new(ENDPOINT.host, ENDPOINT.port)
      http.use_ssl = (ENDPOINT.scheme == "https")

      request = Net::HTTP::Post.new(ENDPOINT)
      request["Content-Type"]  = "application/json"
      request["Accept"]        = "application/json"
      request["Authorization"] = "Bearer #{@access_token}"
      request.body = body

      response = http.request(request)
      JSON.parse(response.body)
    rescue StandardError => e
      { "error" => e.message }
    end
  end
end
