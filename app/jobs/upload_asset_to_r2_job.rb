require "net/http"
require "uri"

class UploadAssetToR2Job < ApplicationJob
  queue_as :default

  retry_on StandardError, wait: :exponentially_longer, attempts: 5

  def perform(song, asset_type_str)
    unless defined?(R2_CLIENT) && R2_CLIENT && defined?(R2_BUCKET_NAME) && R2_BUCKET_NAME
      Rails.logger.error "R2 client or bucket name is not configured. Skipping upload for Song #{song.id}, Asset: #{asset_type_str}."
      return
    end

    asset_type = asset_type_str.to_sym
    source_url = song.public_send("#{asset_type}_url")

    if defined?(R2_PUBLIC_URL_BASE) && R2_PUBLIC_URL_BASE && source_url&.start_with?(R2_PUBLIC_URL_BASE)
      Rails.logger.info "Asset #{asset_type} for Song #{song.id} seems to be already uploaded to R2 (#{source_url}). Skipping."
      return
    end

    unless source_url.present?
      Rails.logger.warn "Source URL for #{asset_type} in Song #{song.id} is blank. Skipping upload."
      return
    end

    uri = URI.parse(source_url)
    content = nil
    content_type = nil

    response = Net::HTTP.start(uri.host, uri.port, use_ssl: uri.scheme == "https", read_timeout: 60) do |http|
      request = Net::HTTP::Get.new uri
      http.request request
    end

    unless response.is_a?(Net::HTTPSuccess)
      raise "HTTP Download Error: #{response.code} #{response.message} for URL: #{source_url}"
    end

    content = response.body
    content_type = response.content_type

    unless content
      raise "Failed to download content (empty body) from #{source_url}"
    end

    path = uri.path.to_s
    extension = File.extname(path)
    r2_key = "#{song.id}#{extension}"

    content_type ||= (asset_type == :audio) ? "audio/mpeg" : "image/jpeg"

    Rails.logger.info "Uploading #{asset_type} for Song #{song.id} to R2 key: #{r2_key}"
    R2_CLIENT.put_object(
      bucket: R2_BUCKET_NAME,
      key: r2_key,
      body: content,
      content_type: content_type,
      cache_control: "public, max-age=2592000" # 30 days cache - standard for user-generated content
    )

    r2_public_url = "#{R2_PUBLIC_URL_BASE.chomp('/')}/#{r2_key}"

    song.update!("#{asset_type}_url": r2_public_url)
  end
end
