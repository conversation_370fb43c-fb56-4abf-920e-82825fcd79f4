class GenerateStreamJob < ApplicationJob
  queue_as :default

  def perform(stream_type, locale, target_id, session_id)
    # --- Setup ---
    @sequence = 0
    @target_id = target_id
    @stream_name = "openai_stream_#{session_id}"

    buffer = ""
    last_broadcast = Time.now
    broadcast_interval = 0.2 # seconds
    buffer_size_limit = 20   # characters

    # --- Reusable stream processing logic ---
    stream_processor = lambda do |content, done, error|
      break if error || done

      if content.present?
        buffer << content
        if Time.now - last_broadcast > broadcast_interval || buffer.length >= buffer_size_limit
          broadcast(buffer) # Call our private helper
          buffer = ""
          last_broadcast = Time.now
        end
      end
    end

    # --- Execute Streaming ---
    service = OpenaiService.new
    case stream_type
    when "surprise_me"
      service.generate_surprise_me_stream(locale, &stream_processor)
    when "lyrics"
      service.generate_lyrics_stream(locale, &stream_processor)
    end

    # --- Finalization ---
    broadcast(buffer) # Flush any remaining content
    broadcast(nil, done: true) # Send completion signal
  end

  private

  def broadcast(content, done: false)
    # Don't send empty messages unless it's the final "done" signal
    return if content.blank? && !done

    # Prepare a JSON payload with the content and its sequence number
    payload = { sequence: @sequence, content: content, done: done }.to_json

    # The payload must be wrapped in an HTML element for Turbo Streams to process it
    html_payload = "<div data-stream-payload='#{ERB::Util.html_escape(payload)}' style='display: none;'></div>"

    # Broadcast the HTML payload to the correct channel
    Turbo::StreamsChannel.broadcast_append_to(@stream_name, target: @target_id, html: html_payload)

    # Increment the sequence for the next chunk
    @sequence += 1
  end
end
