class GenerationSunoSongJob < ApplicationJob
  queue_as :default

  def perform(generation_task_id)
    task = GenerationTask.find_by(id: generation_task_id)
    return unless task

    task.processing_api_request!
    client       = Suno::Client.new
    callback_url = Rails.application.routes.url_helpers.webhooks_api_box_url(
      host: ENV.fetch("APP_HOST")
      # TODO: Will change based on real url in production.
    )

    payload = task.api_payload(callback_url:)

    begin
      api_response = client.generate_music(payload)

      if api_response["error"]
        task.fail_with_refund!(error_data: api_response)
      else
        task_id_in_response = api_response.dig("data", "taskId") || api_response["task_id"] || api_response["id"]
        task.update!(status: :processing_api_request,
                     response_data: api_response,
                     task_id: task_id_in_response)
        task.reload

        # Schedule a job to check for a timeout in 5 minutes.
        CheckGenerationTimeoutJob.set(wait: 10.minutes).perform_later(task.id)
      end
    rescue SocketError, Net::OpenTimeout, Net::ReadTimeout, Errno::ECONNREFUSED => e
      # Catch common network errors and fail the task gracefully.
      task.fail_with_refund!(error_data: { error: e.message })
    end
  end

  private
end
