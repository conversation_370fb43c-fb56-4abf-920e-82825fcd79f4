class Song < ApplicationRecord
  include ActionView::RecordIdentifier

  belongs_to :user
  belongs_to :generation_task, optional: true

  validates :title, presence: true

  scope :liked, -> { where(is_liked: true) }

  after_create_commit :handle_song_creation

  def toggle_like!
    update(is_liked: !is_liked)
  end

  # Search functionality
  scope :search_by_title, ->(query_term) {
    return none if query_term.blank?

    safe_query_term = sanitize_sql_like(query_term)
    where("lower(songs.title) LIKE ?", "%#{safe_query_term.downcase}%")
      .order(
        Arel.sql(sanitize_sql_array([ "CASE WHEN lower(songs.title) LIKE ? THEN 0 ELSE 1 END", "#{safe_query_term.downcase}%" ])),
        "songs.title"
      )
      .limit(10)
  }

  after_update_commit :broadcast_like_change, if: :saved_change_to_is_liked?

  private

  def handle_song_creation
    # Broadcast the new song to the UI
    broadcast_prepend_to [ user, "songs" ],
                         target: "song_cards_list",
                         partial: "shared/song_card",
                         locals: { song: self }

    # Schedule asset uploads
    schedule_asset_uploads
  end

  def broadcast_like_change
    {
      dom_id(self, :like) => :list,
      dom_id(self, :detail_like) => :detail
    }.each do |target, variant|
      broadcast_update_to(
        [ user, "songs" ],
        target:  target,
        partial: "songs/like_button",
        locals:  { song: self, variant: variant }
      )
    end
  end

  def self.sanitize_sql_like(string, escape_character = "\\")
    pattern = Regexp.union(escape_character, "%%", "_")
    string.gsub(pattern) { |char| [ escape_character, char ].join }
  end

  def schedule_asset_uploads
    UploadAssetToR2Job.perform_later(self, "audio") if audio_url.present?
    UploadAssetToR2Job.perform_later(self, "image") if image_url.present?
  end
end
