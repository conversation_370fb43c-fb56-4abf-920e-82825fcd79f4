class Plan < ApplicationRecord
  # === Associations ===
  has_many :orders, dependent: :destroy
  has_many :users, dependent: :nullify

  # === Enums ===
  enum :plan_type, {
    free: 0,
    basic: 1,
    pro: 2,
    unlimited: 3,
    credits: 4
  }

  enum :billing_interval, {
    month: "month",
    year: "year",
    one_time: "credits"
  }

  # === Validations ===
  validates :name, presence: true
  validates :price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :plan_limit, presence: true, numericality: { greater_than_or_equal_to: -1 }
  validates :plan_type, presence: true
  validates :billing_interval, presence: true

  # === Scopes ===
  scope :subscription_plans, -> { where(plan_type: %w[basic pro unlimited]) }
  scope :credit_packages, -> { where(plan_type: %w[credits]) }
  scope :monthly, -> { where(billing_interval: :month) }
  scope :yearly, -> { where(billing_interval: :year) }
  scope :active, -> { where(is_active: true) }
  scope :by_price, -> { order(:price) }

  # === Plan Type Checks ===
  def subscription_plan?
    basic? || pro? || unlimited?
  end

  # Calculate expiry date based on billing interval
  def calculate_expiry_date
    return nil unless subscription_plan?

    case billing_interval
    when "month" then 1.month.from_now
    when "year" then 1.year.from_now
    else nil
    end
  end

  # Get free plan (cached)
  def self.free_plan
    @free_plan ||= find_by!(plan_type: :free)
  end

  # Check if this plan is an upgrade from another plan
  def upgrade_from?(other_plan)
    return true if other_plan.free?

    # Get plan tiers (basic=1, pro=2, unlimited=3)
    current_tier = Plan.plan_types[plan_type]
    other_tier = Plan.plan_types[other_plan.plan_type]

    # Same billing interval: must be higher tier
    if billing_interval == other_plan.billing_interval
      current_tier > other_tier
    # Monthly user can upgrade to yearly plans of same or higher tier
    elsif other_plan.month? && year?
      current_tier >= other_tier
    # Yearly users cannot downgrade to monthly
    else
      false
    end
  end

  # Check if this plan is the same as another plan
  def same_as?(other_plan)
    plan_type == other_plan.plan_type && billing_interval == other_plan.billing_interval
  end
end
