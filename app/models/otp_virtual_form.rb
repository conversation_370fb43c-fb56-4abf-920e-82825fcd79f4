class OtpVirtualForm
  include ActiveModel::Model
  include ActiveModel::Validations

  # 定义常量
  OTP_RESEND_COOLDOWN = 60  # OTP 重发冷却时间（秒）

  attr_accessor :email, :otp, :token, :form_type

  validates :email, presence: { message: I18n.t("auth.errors.invalid_email") }, if: :request_mode?
  validates :email, format: { with: /\A[^@\s]+@[^@\s]+\z/, message: I18n.t("auth.errors.invalid_email") }, if: :request_mode?

  validates :otp, presence: { message: I18n.t("auth.errors.empty_code") }, if: :verify_mode?
  validates :token, presence: { message: I18n.t("auth.errors.verification_failed") }, if: :verify_mode?

  def initialize(attributes = {})
    super
    @form_type ||= :request
  end

  def request_mode?
    @form_type == :request
  end

  def verify_mode?
    @form_type == :verify
  end

  def save
    return false unless valid?

    if request_mode?
      process_otp_request
    elsif verify_mode?
      process_otp_verification
    else
      errors.add(:base, I18n.t("auth.errors.invalid_form_type"))
      false
    end
  end

  def save!
    save || raise(ActiveRecord::RecordInvalid.new(self))
  end

  # 提供验证成功后的邮箱
  def verified_email
    @verified_email
  end

  private

  def process_otp_request
    begin
      # 生成 OTP 和令牌
      result = OtpService.generate_otp_for_user(email)
      @otp = result[:otp]
      @token = result[:token]

      # 根据环境和环境变量决定邮件发送方式
      if Rails.env.development? && !ENV["SEND_REAL_EMAILS"].present?
        # 开发环境且没有设置发送真实邮件，使用 letter_opener
        original_method = ActionMailer::Base.delivery_method
        begin
          ActionMailer::Base.delivery_method = :letter_opener
          OtpMailer.otp_email(email, otp).deliver_now
        ensure
          ActionMailer::Base.delivery_method = original_method
        end
      else
        # 其他环境或设置了发送真实邮件，使用全局配置的 Postmark
        # 使用 deliver_now 代替 deliver_later，以便捕获异常
        OtpMailer.otp_email(email, otp).deliver_now
      end

      true
    rescue Postmark::ApiInputError => e
      # 专门处理 Postmark API 错误
      Rails.logger.error("Postmark API 错误: #{e.message}")
      errors.add(:base, I18n.t("auth.errors.email_delivery_failed"))
      false
    rescue StandardError => e
      # 记录错误到日志
      Rails.logger.error("OTP 邮件发送失败: #{e.message}\n#{e.backtrace.join("\n")}")
      # 添加错误到模型
      errors.add(:base, I18n.t("auth.errors.email_delivery_failed"))
      false
    end
  end

  def process_otp_verification
    begin
      # 验证 OTP
      result = OtpService.verify_otp(token, otp)

      if result[:success]
        @verified_email = result[:email]
        true
      else
        error_code = result[:error] || "verification_failed"
        error_message = case error_code
        when "invalid_otp" then I18n.t("auth.errors.invalid_code")
        when "expired_otp" then I18n.t("auth.errors.expired_code")
        when "used_otp" then I18n.t("auth.errors.used_code")
        else I18n.t("auth.errors.verification_failed")
        end
        errors.add(:base, error_message)
        false
      end
    rescue => e
      # 记录错误到日志
      Rails.logger.error("OTP verification failed: #{e.message}")
      errors.add(:base, I18n.t("auth.errors.verification_failed"))
      false
    end
  end
end
