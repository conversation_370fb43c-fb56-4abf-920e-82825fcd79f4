class PageField < ApplicationRecord
  # === Associations ===
  belongs_to :page,
             foreign_key: :page_id,
             inverse_of: :page_fields

  # === Validations ===
  validates :key, :locale, presence: true
  validates :value, length: { maximum: 10_000 }, allow_nil: true
  validates :key, uniqueness: { scope: %i[page_id locale] }

  # === Scopes ===
  scope :active, -> { where(discarded_at: nil) }
  scope :by_locale, ->(locale) { where(locale: locale) if locale.present? }
end
