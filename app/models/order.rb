class Order < ApplicationRecord
  belongs_to :user
  belongs_to :plan

  enum :status, {
    pending: 0,
    paid: 1,
    completed: 2,
    failed: 3
  }

  validates :amount, presence: true, numericality: { greater_than: 0 }
  validates :currency, presence: true
  validates :plan, presence: true
  validates :stripe_session_id, presence: true, allow_blank: true, uniqueness: true

  scope :recent, -> { order(created_at: :desc) }
  scope :successful, -> { where(status: [ :paid, :completed ]) }

  # Create Stripe checkout session and update order
  def create_checkout_session!(success_url, cancel_url, locale = "en")
    session = Stripe::Checkout::Session.create(stripe_session_params(success_url, cancel_url, locale))
    update!(stripe_session_id: session.id)
    session
  rescue Stripe::StripeError => e
    mark_as_failed!(reason: e.message)
    raise e
  end

  # === PAYMENT PROCESSING (Called from WebhooksController ONLY) ===

  # Mark order as paid and fulfill credits (called from webhook)
  def mark_as_paid!(payment_intent_id: nil)
    transaction do
      update!(
        status: :paid,
        paid_time: Time.current,
        metadata: metadata.merge(payment_intent_id: payment_intent_id).compact
      )
      fulfill_order!
    end
  end

  # Mark order as failed (called from webhook)
  def mark_as_failed!(reason: nil)
    update!(
      status: :failed,
      metadata: metadata.merge(failure_reason: reason).compact
    )
  end

  private

  def stripe_session_params(success_url, cancel_url, locale)
    {
      customer: user.stripe_customer_id_or_create,
      payment_method_types: [ "card" ],
      line_items: [ line_item ],
      mode: "payment",
      success_url: success_url,
      cancel_url: cancel_url,
      metadata: stripe_metadata,
      locale: locale
    }
  end

  def line_item
    credits_description = plan.plan_limit == -1 ? I18n.t("shared.unlimited_credits") : "#{plan.plan_limit} #{I18n.t('shared.credits')}"

    # Translate plan name for Stripe display
    plan_display_name = I18n.t(plan.name)

    {
      price_data: {
        currency: currency.downcase,
        product_data: {
          name: plan_display_name,
          description: credits_description
        },
        unit_amount: amount * 100 # Convert to cents
      },
      quantity: 1
    }
  end

  def stripe_metadata
    {
      order_id: id.to_s,
      user_id: user_id,
      plan_id: plan_id,
      credits: plan.plan_limit.to_s
    }
  end

  # === Order Fulfillment ===
  def fulfill_order!
    return if completed? || !paid? || plan.blank?

    if plan.subscription_plan?
      subscribe_user_to_plan!
    elsif plan.credits?
      add_credits_to_user!
    else
      raise ArgumentError, "Invalid plan_type: #{plan.plan_type}"
    end

    update!(status: :completed)
  rescue => e
    Rails.logger.error "Order fulfillment failed for order #{id}: #{e.message}"
    raise e
  end

  # Subscribe user to a plan (for subscription plans)
  def subscribe_user_to_plan!
    user.transaction do
      user.update!(
        plan: plan,
        plan_used: 0,
        plan_limit: plan.plan_limit,
        plan_expire_at: plan.calculate_expiry_date
      )
      user.increment!(:paid_amount, amount)

      Rails.logger.info "Subscribed user #{user.id} to #{plan.plan_type} plan, set plan_limit to #{plan.plan_limit}. Expires: #{user.plan_expire_at}"
      user.broadcast_plan_card_update
    end
  end

  # Add purchased credits (for credit packages)
  def add_credits_to_user!
    user.transaction do
      user.increment!(:purchased_credits, plan.plan_limit)
      user.increment!(:paid_amount, amount)

      Rails.logger.info "Added #{plan.plan_limit} purchased credits to user #{user.id}. New purchased credits: #{user.purchased_credits}"
      user.broadcast_plan_card_update
    end
  end
end
