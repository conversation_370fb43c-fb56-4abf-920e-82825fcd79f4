class Page < ApplicationRecord
  # === Associations ===
  has_many :page_fields,
           class_name: "<PERSON><PERSON>ield",
           foreign_key: :page_id,
           inverse_of: :page,
           dependent: :destroy

  # === Validations ===
  validates :slug, presence: true, uniqueness: true

  # === Scopes ===
  scope :active, -> { where(discarded_at: nil) }
  scope :discarded, -> { where.not(discarded_at: nil) }

  # === Instance Helpers ===
  # Get the field value for the given key / locale (defaults to current I18n locale)
  # Falls back to English if content not found in requested locale
  def field_value(key, locale: I18n.locale.to_s)
    # Try current locale first
    value = page_fields.find_by(key: key, locale: locale)&.value
    return value if value.present?

    # Fallback to English if not found and current locale is not English
    page_fields.find_by(key: key, locale: "en")&.value
  end
end
