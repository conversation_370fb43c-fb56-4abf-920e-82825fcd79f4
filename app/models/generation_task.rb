class GenerationTask < ApplicationRecord
  belongs_to :user
  has_many :songs, dependent: :nullify

  enum :api_provider, { suno: 0 }
  enum :status, { pending: 0, processing_api_request: 1, text_received: 2, first_data_received: 3, completed: 4, failed: 5 }

  FINAL_STATUSES = %w[completed failed].freeze

  scope :in_progress, -> { where.not(status: [ :completed, :failed ]) }

  validates :ai_model_name, presence: true
  validates :prompt, presence: true, unless: -> { custom_mode && instrumental }
  validate :user_has_sufficient_credits, on: :create
  validate :user_within_concurrency_limit, on: :create

  # Broadcast skeleton cards after creation
  after_create_commit :broadcast_skeleton_cards

  # Broadcast progress updates only for specific, intermediate states.
  after_update_commit :broadcast_progress_updates, if: -> { text_received? || first_data_received? }
  # When the task ultimately fails, make sure the temporary generation cards disappear in real-time.
  after_update_commit :remove_task_cards, if: -> { failed? }
  after_update_commit :broadcast_failure_toast, if: -> { saved_change_to_status? && failed? }

  def self.in_progress_statuses
    statuses.keys - %w[completed failed]
  end

  def self.create_from_form(user, form_params)
    task = user.generation_tasks.new(
      api_provider: :suno,
      ai_model_name: "V3_5",
      status: :pending
    )

    # Build from Form
    task.assign_attributes_from_form(form_params)

    # Save and consume credits in transaction
    task.transaction do
      task.save!
      user.consume_credits!(10)
      GenerationSunoSongJob.perform_later(task.id)
    end

    task # Return the created task
  end

  # Process incoming webhook callbacks
  def process_webhook_callback(callback_type, callback_data)
    self.response_data = callback_data # Store the latest full callback JSON
    items_data = callback_data.dig("data", "data") || []

    case callback_type
    when "text"
      self.status = :text_received
    when "first"
      self.status = :first_data_received
    when "complete"
      self.status = :completed
      self.completed_at = Time.current
      create_songs_from_webhook(items_data)
      remove_task_cards # Remove temporary cards after songs are created / callback type == complete
    when "failed"
      fail_with_refund!(error_data: callback_data)
    else
      raise ArgumentError, "[GenerationTask##{id}] Received unknown callback_type: '#{callback_type}'"
    end

    save! # Let ActiveRecord::RecordInvalid propagate if validations fail
    true   # Indicate successful processing
  end

  # This is our new, simpler method to handle all failure and refund logic.
  def fail_with_refund!(error_data: {})
    Rails.logger.debug "[GenerationTask##{id}] fail_with_refund! called. Current status: #{status}"
    # Safety check: do nothing if the task is already finished.
    if completed? || failed?
      Rails.logger.debug "[GenerationTask##{id}] Task already completed or failed. Aborting fail_with_refund!."
      return
    end

    transaction do
      # Set attributes in memory first, just like the 'completed' path.
      self.status = :failed
      self.error_data = error_data

      user.refund_credits!(10)

      Rails.logger.debug "[GenerationTask##{id}] Saving failed task."
      save!
    end
  end

  # Called by the CheckGenerationTimeoutJob when a task times out.
  def fail_due_to_timeout!
    fail_with_refund!(error_data: { timeout: true, message: "The generation process timed out after 5 minutes." })
  end

  def assign_attributes_from_form(form)
    self.custom_mode = form[:custom_style_enabled].present?
    self.instrumental = form[:instrumental].present? || Array(form[:vocals]).include?("instrumental")

        if custom_mode
      # In custom mode: prompt = lyrics, style = metadata (raw values only)
      self.prompt = instrumental ? "" : form[:lyrics].to_s.strip

      # Build style: use custom style textarea if enabled, otherwise build from metadata
      if form[:use_custom_style].present? && form[:style].present?
        self.style = form[:style].to_s.strip
      else
        # Build style from metadata (no prefixes, just values)
        style_parts = [
          (Array(form[:genres]).join(", ") if form[:genres].present?),
          (Array(form[:moods]).join(", ") if form[:moods].present?),
          (form[:tempo] if form[:tempo].present?),
          (Array(form[:instruments]).join(", ") if form[:instruments].present?),
          (form[:duration] if form[:duration].present?),
          (form[:vocals] if form[:vocals].present? && form[:vocals] != "instrumental")
        ].compact
        self.style = style_parts.reject(&:blank?).join(", ")
      end
      self.title = form[:title]
        else
      # Standard mode
      base = (form[:description] || form[:lyrics]).to_s.strip
      extras = [
        ("Genre: #{Array(form[:genres]).join(', ')}" if form[:genres].present?),
        ("Moods: #{Array(form[:moods]).join(', ')}" if form[:moods].present?),
        ("Tempo: #{form[:tempo]}" if form[:tempo].present?),
        ("Instrument: #{Array(form[:instruments]).join(', ')}" if form[:instruments].present?),
        ("Duration: #{form[:duration]}" if form[:duration].present?),
        ("Vocal: #{form[:vocals]}" if form[:vocals].present? && form[:vocals] != "instrumental")
      ].compact
      self.prompt = ([ base ] + extras).reject(&:blank?).join(" | ")
        end
  end

  # Returns the payload hash to be sent to Suno API
  def api_payload(callback_url:)
    {
      prompt: prompt.presence,
      style: style.presence,
      title: title.presence,
      customMode: custom_mode,
      instrumental: instrumental,
      model: ai_model_name,
      negativeTags: style_negative.presence,
      callBackUrl: callback_url
    }.compact
  end

  def create_songs_from_webhook(items_data)
    return unless items_data.is_a?(Array)

    items_data.each do |item|
      Song.create!(
        user: user,
        generation_task: self,
        title: (item["title"].presence || self.title || "Untitled"), # Use item title, fallback to task's original title
        lyrics: item["prompt"].presence, # Lyrics are in item's "prompt" field
        prompt: self.prompt, # The overall prompt for the GenerationTask
        style: item["tags"].presence,
        status: "completed", # Song status, distinct from task status
        audio_url: item["audio_url"].presence,
        image_url: item["image_url"].presence,
        duration: item["duration"].presence
      )
    end
  end

  # Get streaming audio URLs when text is received or first data is received
  def streaming_audio_urls
    return [] unless (text_received? || first_data_received?) && response_data.present?

    items_data = response_data.dig("data", "data") || []
    items_data.filter_map { |item| item["stream_audio_url"].presence }
  end

  # A simple helper to check if the task is in a non-final state.
  def in_progress?
    !completed? && !failed?
  end

  private

  # Custom validation to check if user has sufficient credits
  def user_has_sufficient_credits
    return unless user.present?

    unless user.has_sufficient_credits?
      errors.add(:base, "Insufficient credits")
    end
  end

  # Custom validation to check concurrency limit
  def user_within_concurrency_limit
    return unless user.present?

    limit = user.plan_concurrency_limit
    active_jobs_count = user.generation_tasks.in_progress.count

    if active_jobs_count >= limit
      errors.add(:base, I18n.t("generation_task.errors.concurrency_limit_reached", limit: limit))
    end
  end

  # Broadcast two skeleton cards when task is created
  def broadcast_skeleton_cards
    2.times do |index|
      broadcast_prepend_to [ user, "songs" ],
                          target: "song_cards_list",
                          partial: "shared/song_card_skeleton",
                          locals: { dom_id: "generation_task_#{id}_#{index}" }
    end
  end

  # Update the skeleton cards with actual task data as it comes in
  def broadcast_progress_updates
    2.times do |index|
      broadcast_replace_to [ user, "songs" ],
                          target: "generation_task_#{id}_#{index}",
                          partial: "shared/generation_task_card",
                          locals: { task: self, index: index }
    end
  end

  # Remove the temporary task cards after songs are created
  def remove_task_cards
    Rails.logger.debug "[GenerationTask##{id}] remove_task_cards called."
    2.times do |index|
      target_id = "generation_task_#{id}_#{index}"
      Rails.logger.debug "[GenerationTask##{id}] Broadcasting remove for target: #{target_id}"
      broadcast_remove_to [ user, "songs" ],
                         target: target_id
    end
  end

  # Broadcast a toast notification to the user when the task fails.
  # Uses the shared/flash_messages partial with explicit locals so that it works
  # outside of a traditional controller-request cycle.
  def broadcast_failure_toast
    message = I18n.t("flash.generation_tasks.fail.error", default: "Generation failed. Your credits have been refunded.")

    user.broadcast_replace_to(
      user,
      :flash_messages,
      target: "toast-container",
      partial: "shared/flash_messages",
      locals: {
        flash_type: "error",
        flash_messages: message
      }
    )
  end
end
