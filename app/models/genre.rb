# app/models/genre.rb
class Genre < ApplicationRecord
  def self.search_by_name(query_term, category = nil, locale = nil)
    return none if query_term.blank?

    safe_query_term = sanitize_sql_like(query_term)
    scope = where("name ILIKE ?", "%#{safe_query_term}%")
    scope = scope.where(category: category) if category.present?
    scope = scope.where(locale: locale) if locale.present?
    scope = scope.order(
      Arel.sql(sanitize_sql_array([ "CASE WHEN name ILIKE ? THEN 0 ELSE 1 END", "#{safe_query_term}%" ])), :name
    )
    scope.limit(10)
  end

  private

  def self.sanitize_sql_like(string, escape_character = "\\")
    pattern = Regexp.union(escape_character, "%%", "_") # Corrected to %% for literal % in SQL
    string.gsub(pattern) { |char| [ escape_character, char ].join }
  end
end
