class User < ApplicationRecord
  has_many :identities, dependent: :destroy
  has_many :sessions, dependent: :destroy
  has_many :generation_tasks, dependent: :destroy
  has_many :songs, through: :generation_tasks
  has_many :orders, dependent: :destroy
  belongs_to :plan

  # === Plan Management Constants ===
  FREE_PLAN_LIMIT = 20

  # Scopes for jobs
  scope :free_users, -> { joins(:plan).where(plans: { plan_type: :free }) }
  scope :expired_non_free, ->(time = Time.current) { joins(:plan).where("plan_expire_at < ? AND plan_expire_at IS NOT NULL AND plans.plan_type != ?", time, Plan.plan_types[:free]) }

  # Pure delegation (plan is never nil)
  delegate :free?, :basic?, :pro?, :unlimited?, :credits?, :plan_type, to: :plan

  # 生成唯一用户名
  def self.generate_unique_username(base_name)
    username = base_name

    # 如果用户名已存在，循环尝试添加不同的随机字符串
    while User.exists?(username: username)
      random_suffix = SecureRandom.alphanumeric(6)
      username = "#{base_name}_#{random_suffix}"
    end

    username
  end

  # 从 OAuth 数据创建或更新用户
  def self.find_or_create_from_omniauth(auth_hash)
    email = auth_hash.info.email

    # 查找已有的用户
    user = find_by(email: email)

    unless user
      ActiveRecord::Base.transaction do
        # 生成唯一用户名
        base_username = auth_hash.info.name
        unique_username = generate_unique_username(base_username)

        # 创建新用户
        user = create(
          email: email,
          username: unique_username,
          avatar_url: auth_hash.info.image,
          plan: Plan.free_plan,
          plan_limit: FREE_PLAN_LIMIT
        )

        # 创建 email 身份
        user.create_email_identity!
      end
    end

    # 创建或更新身份
    user.create_user_identities!(auth_hash)

    user
  end

  # 创建或更新用户身份
  def create_user_identities!(auth_hash)
    identity = identities.find_or_initialize_by(
      provider: auth_hash.provider,
      provider_id: auth_hash.uid
    )

    identity.email = auth_hash.info.email
    identity.identity_data = {
      name: auth_hash.info.name,
      email: auth_hash.info.email,
      image: auth_hash.info.image,
      raw_info: auth_hash.extra.raw_info.to_h
    }
    identity.last_sign_in_at = Time.current

    identity.save!
    identity
  end

  # 创建 email 类型身份
  def create_email_identity!
    identity = identities.find_or_initialize_by(
      provider: "email",
      provider_id: id.to_s
    )

    identity.email = email
    identity.identity_data = {
      name: username,
      email: email
    }
    identity.last_sign_in_at = Time.current

    identity.save!
    identity
  end

  # 通过邮箱查找或创建用户
  def self.find_or_create_by_email(email)
    user = find_by(email: email)

    unless user
      ActiveRecord::Base.transaction do
        # 生成唯一用户名，使用邮箱前缀作为基础用户名
        base_username = email.split("@").first
        unique_username = generate_unique_username(base_username)

        # 创建新用户
        user = create!(
          email: email,
          username: unique_username,
          plan: Plan.free_plan,
          plan_limit: FREE_PLAN_LIMIT
        )

        # 创建 email 身份
        user.create_email_identity!
      end
    end

    user
  end

  # === Plan and Credit Management ===

  # Consume credits with smart fallback: plan credits first, then purchased credits
  def consume_credits!(amount = 10)
    transaction do
      # Handle unlimited plans
      if plan_limit == -1
        # For unlimited plans, just update usage counters without restricting
        increment!(:plan_used, amount)
        increment!(:total_used, amount)
        increment!(:today_used, amount)
        broadcast_plan_card_update
        return
      end

      plan_credits_available = [ plan_limit - plan_used, 0 ].max

      if plan_credits_available >= amount
        # Use plan credits only
        increment!(:plan_used, amount)
      else
        # Use remaining plan credits + purchased credits
        plan_credits_to_use = plan_credits_available
        purchased_credits_to_use = amount - plan_credits_to_use

        increment!(:plan_used, plan_credits_to_use) if plan_credits_to_use > 0
        increment!(:purchased_credits_used, purchased_credits_to_use)
      end

      increment!(:total_used, amount)
      increment!(:today_used, amount)

      # Broadcast plan card update for real-time UI update
      broadcast_plan_card_update
    end
  end

  def refund_credits!(amount = 10)
    return if plan_limit == -1
    transaction do
      increment!(:plan_used, -amount)
      increment!(:total_used, -amount)
      increment!(:today_used, -amount)
      broadcast_plan_card_update
    end
  end

  # Check if user has sufficient credits (plan + purchased)
  def has_sufficient_credits?(amount = 10)
    # Handle unlimited plans
    return true if plan_limit == -1

    total_available_credits >= amount
  end

  # Get total available credits (plan + purchased)
  def total_available_credits
    # Handle unlimited plans
    return Float::INFINITY if plan_limit == -1

    plan_credits_remaining + purchased_credits_remaining
  end

  # Get remaining plan credits
  def plan_credits_remaining
    # Handle unlimited plans
    return Float::INFINITY if plan_limit == -1

    [ plan_limit - plan_used, 0 ].max
  end

  # Get remaining purchased credits
  def purchased_credits_remaining
    [ purchased_credits - purchased_credits_used, 0 ].max
  end

  # Get concurrency limit based on plan
  def plan_concurrency_limit
    case plan.plan_type
    when "free" then 1
    when "basic" then 3
    when "pro" then 10
    when "unlimited" then 15
    else
      1 # Default for any other case
    end
  end

  # === Plan Expiration and Reset Logic ===

  # helper to reset to free plan (used for both daily resets and plan expiration)
  def reset_to_free_plan!
    update!(
      plan: Plan.free_plan,
      plan_limit: FREE_PLAN_LIMIT,
      plan_used: 0,
      today_used: 0,
      plan_expire_at: nil
    )
  end

  # === Stripe Integration ===
  def stripe_customer_id_or_create
    return stripe_customer_id if stripe_customer_id.present?

    customer = Stripe::Customer.create(
      email: email,
      name: username,
      metadata: { user_id: id }
    )

    update!(stripe_customer_id: customer.id)
    customer.id
  end

  # === Turbo Stream Broadcasting ===
  def broadcast_plan_card_update
    broadcast_replace_to(
      self,
      :plan_card,
      target: "plan_card_#{id}",
      partial: "shared/plan_card",
      locals: { user: self }
    )
  end
end
